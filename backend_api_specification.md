# <PERSON><PERSON><PERSON> Backend API Specification (.NET)

## API Structure

The API will follow RESTful principles with the following base URL structure:
```
https://api.mithilanighar.com/api/v1/
```

## Authentication and Authorization

### Authentication Endpoints

#### 1. Register User
- **Endpoint**: `POST /auth/register`
- **Description**: Register a new user
- **Request Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "phoneNumber": "string"
}
```
- **Response**: User object with JWT token

#### 2. Login
- **Endpoint**: `POST /auth/login`
- **Description**: Authenticate a user
- **Request Body**:
```json
{
  "email": "string",
  "password": "string"
}
```
- **Response**: JWT token with user information

#### 3. Refresh Token
- **Endpoint**: `POST /auth/refresh-token`
- **Description**: Refresh an expired JWT token
- **Request Body**:
```json
{
  "refreshToken": "string"
}
```
- **Response**: New JWT token

#### 4. Forgot Password
- **Endpoint**: `POST /auth/forgot-password`
- **Description**: Send password reset email
- **Request Body**:
```json
{
  "email": "string"
}
```

#### 5. Reset Password
- **Endpoint**: `POST /auth/reset-password`
- **Description**: Reset password using token
- **Request Body**:
```json
{
  "token": "string",
  "newPassword": "string"
}
```

### Authorization

The API will use role-based authorization with the following roles:
- **Admin**: Full access to all endpoints
- **Artist**: Access to manage their own artworks and profile
- **Customer**: Access to place orders and manage their profile
- **Guest**: Limited access to public endpoints

JWT tokens will include claims for user ID and role.

## API Endpoints by Feature

### 1. User Management

#### Get User Profile
- **Endpoint**: `GET /users/profile`
- **Auth**: Required
- **Description**: Get current user's profile

#### Update User Profile
- **Endpoint**: `PUT /users/profile`
- **Auth**: Required
- **Description**: Update current user's profile
- **Request Body**: User profile data

#### Get User by ID (Admin)
- **Endpoint**: `GET /users/{id}`
- **Auth**: Admin
- **Description**: Get user by ID

#### Get All Users (Admin)
- **Endpoint**: `GET /users`
- **Auth**: Admin
- **Description**: Get all users with pagination
- **Query Parameters**: page, pageSize, search, sortBy, sortDirection

#### Update User (Admin)
- **Endpoint**: `PUT /users/{id}`
- **Auth**: Admin
- **Description**: Update user by ID
- **Request Body**: User data

#### Delete User (Admin)
- **Endpoint**: `DELETE /users/{id}`
- **Auth**: Admin
- **Description**: Delete user by ID

### 2. Artist Management

#### Get All Artists
- **Endpoint**: `GET /artists`
- **Auth**: None
- **Description**: Get all artists with pagination
- **Query Parameters**: page, pageSize, search, featured, sortBy, sortDirection

#### Get Artist by ID
- **Endpoint**: `GET /artists/{id}`
- **Auth**: None
- **Description**: Get artist details by ID

#### Get Artist Artworks
- **Endpoint**: `GET /artists/{id}/artworks`
- **Auth**: None
- **Description**: Get artworks by artist ID
- **Query Parameters**: page, pageSize, category, forSale, sortBy, sortDirection

#### Create Artist Profile
- **Endpoint**: `POST /artists`
- **Auth**: Required
- **Description**: Create artist profile for current user
- **Request Body**: Artist profile data

#### Update Artist Profile
- **Endpoint**: `PUT /artists/{id}`
- **Auth**: Artist (own profile) or Admin
- **Description**: Update artist profile
- **Request Body**: Artist profile data

#### Delete Artist Profile
- **Endpoint**: `DELETE /artists/{id}`
- **Auth**: Admin
- **Description**: Delete artist profile

#### Add Artist Award
- **Endpoint**: `POST /artists/{id}/awards`
- **Auth**: Artist (own profile) or Admin
- **Description**: Add award to artist profile
- **Request Body**: Award data

#### Update Artist Award
- **Endpoint**: `PUT /artists/{id}/awards/{awardId}`
- **Auth**: Artist (own profile) or Admin
- **Description**: Update artist award
- **Request Body**: Award data

#### Delete Artist Award
- **Endpoint**: `DELETE /artists/{id}/awards/{awardId}`
- **Auth**: Artist (own profile) or Admin
- **Description**: Delete artist award

### 3. Artwork Management

#### Get All Artworks
- **Endpoint**: `GET /artworks`
- **Auth**: None
- **Description**: Get all artworks with pagination
- **Query Parameters**: page, pageSize, category, artist, forSale, featured, priceMin, priceMax, year, sortBy, sortDirection

#### Get Artwork by ID
- **Endpoint**: `GET /artworks/{id}`
- **Auth**: None
- **Description**: Get artwork details by ID

#### Create Artwork
- **Endpoint**: `POST /artworks`
- **Auth**: Artist or Admin
- **Description**: Create new artwork
- **Request Body**: Artwork data

#### Update Artwork
- **Endpoint**: `PUT /artworks/{id}`
- **Auth**: Artist (own artwork) or Admin
- **Description**: Update artwork
- **Request Body**: Artwork data

#### Delete Artwork
- **Endpoint**: `DELETE /artworks/{id}`
- **Auth**: Artist (own artwork) or Admin
- **Description**: Delete artwork

#### Add Artwork Image
- **Endpoint**: `POST /artworks/{id}/images`
- **Auth**: Artist (own artwork) or Admin
- **Description**: Add additional image to artwork
- **Request Body**: Image data

#### Update Artwork Image
- **Endpoint**: `PUT /artworks/{id}/images/{imageId}`
- **Auth**: Artist (own artwork) or Admin
- **Description**: Update artwork image
- **Request Body**: Image data

#### Delete Artwork Image
- **Endpoint**: `DELETE /artworks/{id}/images/{imageId}`
- **Auth**: Artist (own artwork) or Admin
- **Description**: Delete artwork image

#### Get Artwork Categories
- **Endpoint**: `GET /artwork-categories`
- **Auth**: None
- **Description**: Get all artwork categories

#### Create Artwork Category (Admin)
- **Endpoint**: `POST /artwork-categories`
- **Auth**: Admin
- **Description**: Create new artwork category
- **Request Body**: Category data

#### Update Artwork Category (Admin)
- **Endpoint**: `PUT /artwork-categories/{id}`
- **Auth**: Admin
- **Description**: Update artwork category
- **Request Body**: Category data

#### Delete Artwork Category (Admin)
- **Endpoint**: `DELETE /artwork-categories/{id}`
- **Auth**: Admin
- **Description**: Delete artwork category

### 4. Exhibition Management

#### Get All Exhibitions
- **Endpoint**: `GET /exhibitions`
- **Auth**: None
- **Description**: Get all exhibitions with pagination
- **Query Parameters**: page, pageSize, upcoming, past, featured, sortBy, sortDirection

#### Get Exhibition by ID
- **Endpoint**: `GET /exhibitions/{id}`
- **Auth**: None
- **Description**: Get exhibition details by ID

#### Create Exhibition (Admin)
- **Endpoint**: `POST /exhibitions`
- **Auth**: Admin
- **Description**: Create new exhibition
- **Request Body**: Exhibition data

#### Update Exhibition (Admin)
- **Endpoint**: `PUT /exhibitions/{id}`
- **Auth**: Admin
- **Description**: Update exhibition
- **Request Body**: Exhibition data

#### Delete Exhibition (Admin)
- **Endpoint**: `DELETE /exhibitions/{id}`
- **Auth**: Admin
- **Description**: Delete exhibition

#### Add Artwork to Exhibition (Admin)
- **Endpoint**: `POST /exhibitions/{id}/artworks`
- **Auth**: Admin
- **Description**: Add artwork to exhibition
- **Request Body**: Artwork ID

#### Remove Artwork from Exhibition (Admin)
- **Endpoint**: `DELETE /exhibitions/{id}/artworks/{artworkId}`
- **Auth**: Admin
- **Description**: Remove artwork from exhibition

### 5. E-commerce

#### Get All Products
- **Endpoint**: `GET /products`
- **Auth**: None
- **Description**: Get all products with pagination
- **Query Parameters**: page, pageSize, category, priceMin, priceMax, search, inStock, featured, sortBy, sortDirection

#### Get Product by ID
- **Endpoint**: `GET /products/{id}`
- **Auth**: None
- **Description**: Get product details by ID

#### Create Product (Admin)
- **Endpoint**: `POST /products`
- **Auth**: Admin
- **Description**: Create new product
- **Request Body**: Product data

#### Update Product (Admin)
- **Endpoint**: `PUT /products/{id}`
- **Auth**: Admin
- **Description**: Update product
- **Request Body**: Product data

#### Delete Product (Admin)
- **Endpoint**: `DELETE /products/{id}`
- **Auth**: Admin
- **Description**: Delete product

#### Get Product Categories
- **Endpoint**: `GET /product-categories`
- **Auth**: None
- **Description**: Get all product categories

#### Create Order
- **Endpoint**: `POST /orders`
- **Auth**: Required
- **Description**: Create new order
- **Request Body**: Order data with items

#### Get Order by ID
- **Endpoint**: `GET /orders/{id}`
- **Auth**: Customer (own order) or Admin
- **Description**: Get order details by ID

#### Get User Orders
- **Endpoint**: `GET /orders`
- **Auth**: Required
- **Description**: Get current user's orders
- **Query Parameters**: page, pageSize, status, sortBy, sortDirection

#### Update Order Status (Admin)
- **Endpoint**: `PUT /orders/{id}/status`
- **Auth**: Admin
- **Description**: Update order status
- **Request Body**: Status data

#### Add Product Review
- **Endpoint**: `POST /products/{id}/reviews`
- **Auth**: Customer
- **Description**: Add review for a product
- **Request Body**: Review data

#### Get Product Reviews
- **Endpoint**: `GET /products/{id}/reviews`
- **Auth**: None
- **Description**: Get reviews for a product
- **Query Parameters**: page, pageSize, rating, sortBy, sortDirection

### 6. Blog Management

#### Get All Blog Posts
- **Endpoint**: `GET /blog/posts`
- **Auth**: None
- **Description**: Get all blog posts with pagination
- **Query Parameters**: page, pageSize, category, tag, featured, author, search, sortBy, sortDirection

#### Get Blog Post by ID or Slug
- **Endpoint**: `GET /blog/posts/{idOrSlug}`
- **Auth**: None
- **Description**: Get blog post details by ID or slug

#### Create Blog Post
- **Endpoint**: `POST /blog/posts`
- **Auth**: Admin
- **Description**: Create new blog post
- **Request Body**: Blog post data

#### Update Blog Post
- **Endpoint**: `PUT /blog/posts/{id}`
- **Auth**: Admin
- **Description**: Update blog post
- **Request Body**: Blog post data

#### Delete Blog Post
- **Endpoint**: `DELETE /blog/posts/{id}`
- **Auth**: Admin
- **Description**: Delete blog post

#### Get Blog Categories
- **Endpoint**: `GET /blog/categories`
- **Auth**: None
- **Description**: Get all blog categories

#### Create Blog Category (Admin)
- **Endpoint**: `POST /blog/categories`
- **Auth**: Admin
- **Description**: Create new blog category
- **Request Body**: Category data

#### Update Blog Category (Admin)
- **Endpoint**: `PUT /blog/categories/{id}`
- **Auth**: Admin
- **Description**: Update blog category
- **Request Body**: Category data

#### Delete Blog Category (Admin)
- **Endpoint**: `DELETE /blog/categories/{id}`
- **Auth**: Admin
- **Description**: Delete blog category

#### Add Comment to Blog Post
- **Endpoint**: `POST /blog/posts/{id}/comments`
- **Auth**: Optional
- **Description**: Add comment to blog post
- **Request Body**: Comment data

#### Get Blog Post Comments
- **Endpoint**: `GET /blog/posts/{id}/comments`
- **Auth**: None
- **Description**: Get comments for a blog post
- **Query Parameters**: page, pageSize, sortBy, sortDirection

### 7. Contact and Inquiry Management

#### Submit Contact Form
- **Endpoint**: `POST /contact`
- **Auth**: None
- **Description**: Submit contact form
- **Request Body**: Contact form data

#### Get All Contact Submissions (Admin)
- **Endpoint**: `GET /contact`
- **Auth**: Admin
- **Description**: Get all contact form submissions
- **Query Parameters**: page, pageSize, status, sortBy, sortDirection

#### Update Contact Submission Status (Admin)
- **Endpoint**: `PUT /contact/{id}/status`
- **Auth**: Admin
- **Description**: Update contact submission status
- **Request Body**: Status data

#### Subscribe to Newsletter
- **Endpoint**: `POST /newsletter/subscribe`
- **Auth**: None
- **Description**: Subscribe to newsletter
- **Request Body**: Email address

#### Unsubscribe from Newsletter
- **Endpoint**: `POST /newsletter/unsubscribe`
- **Auth**: None
- **Description**: Unsubscribe from newsletter
- **Request Body**: Email address

### 8. Content Management

#### Get Page by Slug
- **Endpoint**: `GET /pages/{slug}`
- **Auth**: None
- **Description**: Get page content by slug

#### Create Page (Admin)
- **Endpoint**: `POST /pages`
- **Auth**: Admin
- **Description**: Create new page
- **Request Body**: Page data

#### Update Page (Admin)
- **Endpoint**: `PUT /pages/{id}`
- **Auth**: Admin
- **Description**: Update page
- **Request Body**: Page data

#### Delete Page (Admin)
- **Endpoint**: `DELETE /pages/{id}`
- **Auth**: Admin
- **Description**: Delete page

#### Get Site Settings (Admin)
- **Endpoint**: `GET /settings`
- **Auth**: Admin
- **Description**: Get all site settings
- **Query Parameters**: group

#### Update Site Settings (Admin)
- **Endpoint**: `PUT /settings`
- **Auth**: Admin
- **Description**: Update site settings
- **Request Body**: Settings data

#### Upload Media
- **Endpoint**: `POST /media`
- **Auth**: Admin or Artist
- **Description**: Upload media file
- **Request Body**: Multipart form data with file

#### Get Media by ID
- **Endpoint**: `GET /media/{id}`
- **Auth**: None
- **Description**: Get media item by ID

#### Delete Media
- **Endpoint**: `DELETE /media/{id}`
- **Auth**: Admin or Artist (own media)
- **Description**: Delete media item

## Implementation Notes

### Database Context

```csharp
public class MithilaniGharDbContext : DbContext
{
    public MithilaniGharDbContext(DbContextOptions<MithilaniGharDbContext> options)
        : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Artist> Artists { get; set; }
    public DbSet<ArtistAward> ArtistAwards { get; set; }
    public DbSet<Artwork> Artworks { get; set; }
    public DbSet<ArtworkImage> ArtworkImages { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Tag> Tags { get; set; }
    public DbSet<ArtworkTag> ArtworkTags { get; set; }
    public DbSet<Exhibition> Exhibitions { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<ProductImage> ProductImages { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderItem> OrderItems { get; set; }
    public DbSet<Review> Reviews { get; set; }
    public DbSet<BlogCategory> BlogCategories { get; set; }
    public DbSet<BlogPost> BlogPosts { get; set; }
    public DbSet<BlogPostTag> BlogPostTags { get; set; }
    public DbSet<Comment> Comments { get; set; }
    public DbSet<ContactForm> ContactForms { get; set; }
    public DbSet<NewsletterSubscription> NewsletterSubscriptions { get; set; }
    public DbSet<Page> Pages { get; set; }
    public DbSet<SiteSettings> SiteSettings { get; set; }
    public DbSet<MediaItem> MediaItems { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure composite keys
        modelBuilder.Entity<ArtworkTag>()
            .HasKey(at => new { at.ArtworkId, at.TagId });

        modelBuilder.Entity<BlogPostTag>()
            .HasKey(bt => new { bt.BlogPostId, bt.TagId });

        // Configure relationships
        modelBuilder.Entity<ArtworkTag>()
            .HasOne(at => at.Artwork)
            .WithMany(a => a.ArtworkTags)
            .HasForeignKey(at => at.ArtworkId);

        modelBuilder.Entity<ArtworkTag>()
            .HasOne(at => at.Tag)
            .WithMany(t => t.ArtworkTags)
            .HasForeignKey(at => at.TagId);

        modelBuilder.Entity<BlogPostTag>()
            .HasOne(bt => bt.BlogPost)
            .WithMany(b => b.BlogPostTags)
            .HasForeignKey(bt => bt.BlogPostId);

        modelBuilder.Entity<BlogPostTag>()
            .HasOne(bt => bt.Tag)
            .WithMany(t => t.BlogPostTags)
            .HasForeignKey(bt => bt.TagId);

        // Additional configurations...
    }
}
```

### Repository Pattern

Implement the repository pattern for data access:

```csharp
public interface IRepository<T> where T : class
{
    Task<IEnumerable<T>> GetAllAsync();
    Task<T> GetByIdAsync(int id);
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task DeleteAsync(T entity);
}

public class Repository<T> : IRepository<T> where T : class
{
    protected readonly MithilaniGharDbContext _context;
    
    public Repository(MithilaniGharDbContext context)
    {
        _context = context;
    }
    
    // Implementation of interface methods...
}
```

### Service Layer

Implement service classes for business logic:

```csharp
public interface IArtworkService
{
    Task<IEnumerable<ArtworkDto>> GetAllArtworksAsync(ArtworkFilterParams filterParams);
    Task<ArtworkDto> GetArtworkByIdAsync(int id);
    Task<ArtworkDto> CreateArtworkAsync(ArtworkCreateDto artworkDto, int artistId);
    Task<ArtworkDto> UpdateArtworkAsync(int id, ArtworkUpdateDto artworkDto, int artistId);
    Task DeleteArtworkAsync(int id, int artistId);
    // Additional methods...
}

public class ArtworkService : IArtworkService
{
    private readonly IRepository<Artwork> _artworkRepository;
    private readonly IRepository<Artist> _artistRepository;
    // Additional repositories...
    
    public ArtworkService(
        IRepository<Artwork> artworkRepository,
        IRepository<Artist> artistRepository)
    {
        _artworkRepository = artworkRepository;
        _artistRepository = artistRepository;
    }
    
    // Implementation of interface methods...
}
```

### Controller Example

```csharp
[ApiController]
[Route("api/v1/artworks")]
public class ArtworksController : ControllerBase
{
    private readonly IArtworkService _artworkService;
    
    public ArtworksController(IArtworkService artworkService)
    {
        _artworkService = artworkService;
    }
    
    [HttpGet]
    public async Task<ActionResult<IEnumerable<ArtworkDto>>> GetArtworks([FromQuery] ArtworkFilterParams filterParams)
    {
        var artworks = await _artworkService.GetAllArtworksAsync(filterParams);
        return Ok(artworks);
    }
    
    [HttpGet("{id}")]
    public async Task<ActionResult<ArtworkDto>> GetArtwork(int id)
    {
        var artwork = await _artworkService.GetArtworkByIdAsync(id);
        if (artwork == null)
            return NotFound();
            
        return Ok(artwork);
    }
    
    [HttpPost]
    [Authorize(Roles = "Artist,Admin")]
    public async Task<ActionResult<ArtworkDto>> CreateArtwork(ArtworkCreateDto artworkDto)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();
            
        var artistId = int.Parse(userId);
        var createdArtwork = await _artworkService.CreateArtworkAsync(artworkDto, artistId);
        
        return CreatedAtAction(
            nameof(GetArtwork),
            new { id = createdArtwork.Id },
            createdArtwork);
    }
    
    // Additional endpoints...
}
```
