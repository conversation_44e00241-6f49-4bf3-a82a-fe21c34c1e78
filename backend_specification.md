# <PERSON><PERSON><PERSON> Backend Specification (.NET)

## Technology Stack

- **Framework**: ASP.NET Core 8.0
- **Database**: SQL Server / PostgreSQL
- **ORM**: Entity Framework Core
- **Authentication**: JWT-based authentication
- **API Documentation**: Swagger / OpenAPI
- **File Storage**: Azure Blob Storage / AWS S3
- **Caching**: Redis (optional for performance)
- **Logging**: Serilog

## Database Schema and Models

### 1. User Management

#### User Model
```csharp
public class User
{
    public int Id { get; set; }
    public string Username { get; set; }
    public string Email { get; set; }
    public string PasswordHash { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string PhoneNumber { get; set; }
    public string Address { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Country { get; set; }
    public string PostalCode { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string Role { get; set; } // Ad<PERSON>, Customer, Artist
    public bool IsActive { get; set; }
    public string ProfileImageUrl { get; set; }
    
    // Navigation properties
    public virtual ICollection<Order> Orders { get; set; }
    public virtual Artist Artist { get; set; } // If user is an artist
    public virtual ICollection<Review> Reviews { get; set; }
}
```

#### Role Model
```csharp
public class Role
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    
    // Navigation properties
    public virtual ICollection<User> Users { get; set; }
}
```

### 2. Artist Management

#### Artist Model
```csharp
public class Artist
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string Bio { get; set; }
    public string LongBio { get; set; }
    public string Quote { get; set; }
    public string Specialization { get; set; }
    public string Education { get; set; }
    public string Experience { get; set; }
    public bool IsFeatured { get; set; }
    public string FeaturedImageUrl { get; set; }
    public string ProfileImageUrl { get; set; }
    public string CoverImageUrl { get; set; }
    public DateTime JoinedDate { get; set; }
    public string Website { get; set; }
    public string FacebookUrl { get; set; }
    public string InstagramUrl { get; set; }
    public string TwitterUrl { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; }
    public virtual ICollection<Artwork> Artworks { get; set; }
    public virtual ICollection<Exhibition> Exhibitions { get; set; }
    public virtual ICollection<ArtistAward> Awards { get; set; }
}
```

#### ArtistAward Model
```csharp
public class ArtistAward
{
    public int Id { get; set; }
    public int ArtistId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime AwardDate { get; set; }
    public string Organization { get; set; }
    public string ImageUrl { get; set; }
    
    // Navigation property
    public virtual Artist Artist { get; set; }
}
```

### 3. Artwork Management

#### Artwork Model
```csharp
public class Artwork
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public int ArtistId { get; set; }
    public int CategoryId { get; set; }
    public decimal Price { get; set; }
    public bool IsForSale { get; set; }
    public int Year { get; set; }
    public string Medium { get; set; }
    public string Dimensions { get; set; }
    public string MainImageUrl { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public virtual Artist Artist { get; set; }
    public virtual Category Category { get; set; }
    public virtual ICollection<ArtworkImage> AdditionalImages { get; set; }
    public virtual ICollection<ArtworkTag> ArtworkTags { get; set; }
    public virtual Product Product { get; set; } // If artwork is for sale
    public virtual ICollection<Exhibition> Exhibitions { get; set; }
}
```

#### ArtworkImage Model
```csharp
public class ArtworkImage
{
    public int Id { get; set; }
    public int ArtworkId { get; set; }
    public string ImageUrl { get; set; }
    public string Caption { get; set; }
    public int DisplayOrder { get; set; }
    
    // Navigation property
    public virtual Artwork Artwork { get; set; }
}
```

#### Category Model
```csharp
public class Category
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ImageUrl { get; set; }
    public string Color { get; set; } // For UI styling
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    
    // Navigation properties
    public virtual ICollection<Artwork> Artworks { get; set; }
    public virtual ICollection<Product> Products { get; set; }
}
```

#### Tag Model
```csharp
public class Tag
{
    public int Id { get; set; }
    public string Name { get; set; }
    
    // Navigation properties
    public virtual ICollection<ArtworkTag> ArtworkTags { get; set; }
    public virtual ICollection<BlogPostTag> BlogPostTags { get; set; }
}
```

#### ArtworkTag Model (Junction Table)
```csharp
public class ArtworkTag
{
    public int ArtworkId { get; set; }
    public int TagId { get; set; }
    
    // Navigation properties
    public virtual Artwork Artwork { get; set; }
    public virtual Tag Tag { get; set; }
}
```

### 4. Exhibition Management

#### Exhibition Model
```csharp
public class Exhibition
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Location { get; set; }
    public string Address { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Country { get; set; }
    public string ImageUrl { get; set; }
    public bool IsActive { get; set; }
    public bool IsFeatured { get; set; }
    
    // Navigation properties
    public virtual ICollection<Artwork> Artworks { get; set; }
    public virtual ICollection<Artist> Artists { get; set; }
}
```

### 5. E-commerce

#### Product Model
```csharp
public class Product
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal Price { get; set; }
    public decimal DiscountPrice { get; set; }
    public bool HasDiscount { get; set; }
    public int StockQuantity { get; set; }
    public string SKU { get; set; }
    public int CategoryId { get; set; }
    public string MainImageUrl { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int? ArtworkId { get; set; } // Optional, if product is an artwork
    
    // Navigation properties
    public virtual Category Category { get; set; }
    public virtual Artwork Artwork { get; set; }
    public virtual ICollection<ProductImage> AdditionalImages { get; set; }
    public virtual ICollection<OrderItem> OrderItems { get; set; }
    public virtual ICollection<Review> Reviews { get; set; }
}
```

#### ProductImage Model
```csharp
public class ProductImage
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ImageUrl { get; set; }
    public string Caption { get; set; }
    public int DisplayOrder { get; set; }
    
    // Navigation property
    public virtual Product Product { get; set; }
}
```

#### Order Model
```csharp
public class Order
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string OrderNumber { get; set; }
    public DateTime OrderDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string Status { get; set; } // Pending, Processing, Shipped, Delivered, Cancelled
    public string PaymentMethod { get; set; }
    public string PaymentStatus { get; set; }
    public string ShippingMethod { get; set; }
    public decimal ShippingCost { get; set; }
    public decimal TaxAmount { get; set; }
    public string ShippingFirstName { get; set; }
    public string ShippingLastName { get; set; }
    public string ShippingAddress { get; set; }
    public string ShippingCity { get; set; }
    public string ShippingState { get; set; }
    public string ShippingCountry { get; set; }
    public string ShippingPostalCode { get; set; }
    public string ShippingPhone { get; set; }
    public string ShippingEmail { get; set; }
    public string Notes { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; }
    public virtual ICollection<OrderItem> OrderItems { get; set; }
}
```

#### OrderItem Model
```csharp
public class OrderItem
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal Subtotal { get; set; }
    
    // Navigation properties
    public virtual Order Order { get; set; }
    public virtual Product Product { get; set; }
}
```

#### Review Model
```csharp
public class Review
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int ProductId { get; set; }
    public int Rating { get; set; } // 1-5
    public string Title { get; set; }
    public string Comment { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsApproved { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; }
    public virtual Product Product { get; set; }
}
```

### 6. Blog Management

#### BlogCategory Model
```csharp
public class BlogCategory
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Slug { get; set; }
    public string ImageUrl { get; set; }
    public string Color { get; set; } // For UI styling
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    
    // Navigation properties
    public virtual ICollection<BlogPost> BlogPosts { get; set; }
}
```

#### BlogPost Model
```csharp
public class BlogPost
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string Slug { get; set; }
    public string Excerpt { get; set; }
    public string Content { get; set; }
    public int AuthorId { get; set; } // References User Id
    public int CategoryId { get; set; }
    public string FeaturedImageUrl { get; set; }
    public DateTime PublishedDate { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsPublished { get; set; }
    public int ReadTime { get; set; } // In minutes
    public int ViewCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation properties
    public virtual User Author { get; set; }
    public virtual BlogCategory Category { get; set; }
    public virtual ICollection<BlogPostTag> BlogPostTags { get; set; }
    public virtual ICollection<Comment> Comments { get; set; }
}
```

#### BlogPostTag Model (Junction Table)
```csharp
public class BlogPostTag
{
    public int BlogPostId { get; set; }
    public int TagId { get; set; }
    
    // Navigation properties
    public virtual BlogPost BlogPost { get; set; }
    public virtual Tag Tag { get; set; }
}
```

#### Comment Model
```csharp
public class Comment
{
    public int Id { get; set; }
    public int BlogPostId { get; set; }
    public int? UserId { get; set; } // Optional, for guest comments
    public string GuestName { get; set; }
    public string GuestEmail { get; set; }
    public string Content { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsApproved { get; set; }
    public int? ParentCommentId { get; set; } // For nested comments
    
    // Navigation properties
    public virtual BlogPost BlogPost { get; set; }
    public virtual User User { get; set; }
    public virtual Comment ParentComment { get; set; }
    public virtual ICollection<Comment> Replies { get; set; }
}
```

### 7. Contact and Inquiry Management

#### ContactForm Model
```csharp
public class ContactForm
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Subject { get; set; }
    public string Message { get; set; }
    public DateTime SubmittedAt { get; set; }
    public bool IsRead { get; set; }
    public string Status { get; set; } // New, In Progress, Resolved
    public string Notes { get; set; }
}
```

#### Newsletter Model
```csharp
public class NewsletterSubscription
{
    public int Id { get; set; }
    public string Email { get; set; }
    public DateTime SubscribedAt { get; set; }
    public bool IsActive { get; set; }
    public string Source { get; set; } // Where they subscribed from
}
```

### 8. Content Management

#### Page Model
```csharp
public class Page
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string Slug { get; set; }
    public string Content { get; set; }
    public string MetaTitle { get; set; }
    public string MetaDescription { get; set; }
    public bool IsPublished { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

#### SiteSettings Model
```csharp
public class SiteSettings
{
    public int Id { get; set; }
    public string SettingKey { get; set; }
    public string SettingValue { get; set; }
    public string SettingGroup { get; set; }
    public string Description { get; set; }
}
```

#### MediaItem Model
```csharp
public class MediaItem
{
    public int Id { get; set; }
    public string FileName { get; set; }
    public string FileUrl { get; set; }
    public string FileType { get; set; }
    public long FileSize { get; set; }
    public string AltText { get; set; }
    public string Caption { get; set; }
    public DateTime UploadedAt { get; set; }
    public int? UploadedById { get; set; }
    
    // Navigation property
    public virtual User UploadedBy { get; set; }
}
```
