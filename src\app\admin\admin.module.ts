import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { AdminComponent } from './admin.component';
import { ServiceProxyModule } from '../../shared/service-proxies/service-proxy.module';
import { ProductViewComponent } from './product-view/product-view.component';

@NgModule({
  declarations: [],
  imports: [
    RouterModule.forChild([
      {
        path: '',
        children: [
          {
            path: '',
            component: AdminComponent,
            children: [
              { path: '', redirectTo: 'admin-membership', pathMatch: 'full' },
              { path: "productss", component: ProductViewComponent   },

            ],
          },
          { path: '**', redirectTo: '' },
        ],
      },
    ]),
    ServiceProxyModule,
  ],
})
export class AdminModule {}
