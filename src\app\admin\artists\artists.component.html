<div class="container mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-semibold text-gray-700">Artist Management</h2>
    <button 
      (click)="openAddModal()"
      class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-md active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
      <span class="material-icons align-middle mr-1 text-sm">add</span>
      Add Artist
    </button>
  </div>
  
  <!-- Filters -->
  <div class="mb-6 flex flex-col md:flex-row gap-4">
    <div class="flex-1">
      <label class="block text-sm text-gray-700 mb-2">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span class="material-icons text-gray-400 text-sm">search</span>
        </div>
        <input 
          type="text" 
          [(ngModel)]="searchTerm"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
          placeholder="Search by name, bio, or location">
      </div>
    </div>
    
    <div class="w-full md:w-64">
      <label class="block text-sm text-gray-700 mb-2">Specialty</label>
      <select 
        [(ngModel)]="specialtyFilter"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring focus:ring-purple-200 focus:ring-opacity-50 focus:border-purple-300">
        <option value="">All Specialties</option>
        <option *ngFor="let specialty of specialties" [value]="specialty">{{ specialty }}</option>
      </select>
    </div>
  </div>
  
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center my-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
  
  <!-- Artists Grid -->
  <div *ngIf="!isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let artist of filteredArtists" class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="relative">
        <img [src]="artist.profileImage" [alt]="artist.name" class="w-full h-48 object-cover">
        <div class="absolute top-2 right-2 flex space-x-1">
          <button 
            (click)="openEditModal(artist)"
            class="p-1 text-white bg-blue-500 rounded-full hover:bg-blue-600">
            <span class="material-icons text-sm">edit</span>
          </button>
          <button 
            (click)="openDeleteModal(artist)"
            class="p-1 text-white bg-red-500 rounded-full hover:bg-red-600">
            <span class="material-icons text-sm">delete</span>
          </button>
        </div>
      </div>
      <div class="p-4">
        <h3 class="text-lg font-semibold text-gray-800">{{ artist.name }}</h3>
        <p class="text-sm text-gray-600 mb-2">{{ artist.location }}</p>
        <div class="flex items-center mb-3">
          <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
            {{ artist.specialty }}
          </span>
          <span *ngIf="artist.featured" class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
            Featured
          </span>
        </div>
        <p class="text-sm text-gray-700 line-clamp-3">{{ artist.bio }}</p>
        <div class="mt-3 flex justify-between items-center">
          <span class="text-sm text-gray-500">{{ artist.artworks }} artworks</span>
          <a href="#" class="text-sm font-medium text-indigo-600 hover:text-indigo-500">View Profile</a>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Empty State -->
  <div *ngIf="!isLoading && filteredArtists.length === 0" class="text-center py-8">
    <span class="material-icons text-gray-400 text-5xl mb-4">people</span>
    <h3 class="text-lg font-medium text-gray-600 mb-2">No artists found</h3>
    <p class="text-gray-500">Try adjusting your search or filters</p>
  </div>
  
  <!-- Add/Edit Modal -->
  <div *ngIf="showModal && (modalType === 'add' || modalType === 'edit')" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ modalType === 'add' ? 'Add New Artist' : 'Edit Artist' }}
          </h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input 
                type="text" 
                [(ngModel)]="selectedArtist!.name"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <input 
                type="text" 
                [(ngModel)]="selectedArtist!.location"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Specialty</label>
              <select 
                [(ngModel)]="selectedArtist!.specialty"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                <option *ngFor="let specialty of specialties" [value]="specialty">{{ specialty }}</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
              <textarea 
                [(ngModel)]="selectedArtist!.bio"
                rows="4"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Profile Image URL</label>
              <input 
                type="text" 
                [(ngModel)]="selectedArtist!.profileImage"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input 
                type="email" 
                [(ngModel)]="selectedArtist!.email"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input 
                type="text" 
                [(ngModel)]="selectedArtist!.phone"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="featured" 
                [(ngModel)]="selectedArtist!.featured"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
              <label for="featured" class="ml-2 block text-sm text-gray-900">
                Featured Artist
              </label>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="saveArtist()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm">
            {{ modalType === 'add' ? 'Add' : 'Save' }}
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Confirmation Modal -->
  <div *ngIf="showModal && modalType === 'delete'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <span class="material-icons text-red-600">warning</span>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Delete Artist
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Are you sure you want to delete "{{ selectedArtist?.name }}"? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="deleteArtist()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
