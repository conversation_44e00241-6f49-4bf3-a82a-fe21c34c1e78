import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ArtistsService } from '../services/artists.service';
import { Artist } from '../models/artist.model';

@Component({
  selector: 'app-artists',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './artists.component.html',
  styleUrls: ['./artists.component.css']
})
export class ArtistsComponent implements OnInit {
  artists: Artist[] = [];
  selectedArtist: Artist | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  
  // Filters
  searchTerm = '';
  specialtyFilter = '';
  specialties = ['Painting', 'Sculpture', 'Mixed Media', 'Digital Art', 'Photography'];
  
  constructor(private artistsService: ArtistsService) {}

  ngOnInit(): void {
    this.loadArtists();
  }
  
  loadArtists(): void {
    this.isLoading = true;
    this.artistsService.getArtists().subscribe({
      next: (artists) => {
        this.artists = artists;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading artists', error);
        this.isLoading = false;
      }
    });
  }
  
  openAddModal(): void {
    this.selectedArtist = new Artist();
    this.modalType = 'add';
    this.showModal = true;
  }
  
  openEditModal(artist: Artist): void {
    this.selectedArtist = { ...artist };
    this.modalType = 'edit';
    this.showModal = true;
  }
  
  openDeleteModal(artist: Artist): void {
    this.selectedArtist = artist;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedArtist = null;
  }
  
  saveArtist(): void {
    if (!this.selectedArtist) return;
    
    if (this.modalType === 'add') {
      this.artistsService.addArtist(this.selectedArtist).subscribe({
        next: () => {
          this.loadArtists();
          this.closeModal();
        },
        error: (error) => console.error('Error adding artist', error)
      });
    } else if (this.modalType === 'edit') {
      this.artistsService.updateArtist(this.selectedArtist).subscribe({
        next: () => {
          this.loadArtists();
          this.closeModal();
        },
        error: (error) => console.error('Error updating artist', error)
      });
    }
  }
  
  deleteArtist(): void {
    if (!this.selectedArtist) return;
    
    this.artistsService.deleteArtist(this.selectedArtist.id).subscribe({
      next: () => {
        this.loadArtists();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting artist', error)
    });
  }
  
  get filteredArtists(): Artist[] {
    return this.artists.filter(artist => {
      const matchesSearch = this.searchTerm === '' || 
        artist.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artist.bio.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artist.location.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesSpecialty = this.specialtyFilter === '' || 
        artist.specialty === this.specialtyFilter;
        
      return matchesSearch && matchesSpecialty;
    });
  }
}
