<div class="container mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-semibold text-gray-700">Blog Management</h2>
    <button
      (click)="openAddModal()"
      class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-md active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
      <span class="material-icons align-middle mr-1 text-sm">add</span>
      Add Blog Post
    </button>
  </div>

  <!-- Filters -->
  <div class="mb-6 flex flex-col md:flex-row gap-4">
    <div class="flex-1">
      <label class="block text-sm text-gray-700 mb-2">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span class="material-icons text-gray-400 text-sm">search</span>
        </div>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
          placeholder="Search by title, content, or author">
      </div>
    </div>

    <div class="w-full md:w-64">
      <label class="block text-sm text-gray-700 mb-2">Category</label>
      <select
        [(ngModel)]="categoryFilter"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring focus:ring-purple-200 focus:ring-opacity-50 focus:border-purple-300">
        <option value="">All Categories</option>
        <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
      </select>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center my-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>

  <!-- Blog Posts List -->
  <div *ngIf="!isLoading" class="grid grid-cols-1 gap-6">
    <div *ngFor="let post of filteredBlogPosts" class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="md:flex">
        <div class="md:flex-shrink-0">
          <img class="h-48 w-full object-cover md:w-48" [src]="post.imageUrl" [alt]="post.title">
        </div>
        <div class="p-6 flex flex-col justify-between">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-indigo-600">{{ post.category }}</span>
              <div class="flex space-x-2">
                <button
                  (click)="openEditModal(post)"
                  class="p-1 text-blue-600 rounded-full hover:bg-blue-100">
                  <span class="material-icons text-sm">edit</span>
                </button>
                <button
                  (click)="openDeleteModal(post)"
                  class="p-1 text-red-600 rounded-full hover:bg-red-100">
                  <span class="material-icons text-sm">delete</span>
                </button>
              </div>
            </div>
            <a href="#" class="block mt-2">
              <p class="text-xl font-semibold text-gray-900">{{ post.title }}</p>
              <p class="mt-3 text-gray-500 line-clamp-3">{{ post.content }}</p>
            </a>
          </div>
          <div class="mt-4 flex items-center">
            <div class="flex-shrink-0">
              <img class="h-10 w-10 rounded-full" [src]="post.authorImageUrl || 'https://ui-avatars.com/api/?name=' + post.author" [alt]="post.author">
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">{{ post.author }}</p>
              <div class="flex space-x-1 text-sm text-gray-500">
                <time>{{ post.publishDate?.toJSDate() | date }}</time>
                <span aria-hidden="true">&middot;</span>
                <span>{{ post.status }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && filteredBlogPosts.length === 0" class="text-center py-8">
    <span class="material-icons text-gray-400 text-5xl mb-4">article</span>
    <h3 class="text-lg font-medium text-gray-600 mb-2">No blog posts found</h3>
    <p class="text-gray-500">Try adjusting your search or filters</p>
  </div>

  <!-- Add/Edit Modal -->
  <div *ngIf="showModal && (modalType === 'add' || modalType === 'edit')" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ modalType === 'add' ? 'Add New Blog Post' : 'Edit Blog Post' }}
          </h3>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Title <span class="text-red-500">*</span></label>
              <input
                type="text"
                [(ngModel)]="selectedPost!.title"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Author <span class="text-red-500">*</span></label>
              <input
                type="text"
                [(ngModel)]="selectedPost!.author"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Category <span class="text-red-500">*</span></label>
              <select
                [(ngModel)]="selectedPost!.category"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                <option value="">Select a category</option>
                <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Content</label>
              <textarea
                [(ngModel)]="selectedPost!.content"
                rows="6"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
              <input
                type="text"
                [(ngModel)]="selectedPost!.imageUrl"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Tags <span class="text-red-500">*</span></label>
              <input
                type="text"
                [(ngModel)]="tagsInput"
                placeholder="Enter tags separated by commas (e.g., art, culture, mithila)"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              <p class="text-xs text-gray-500 mt-1">Separate multiple tags with commas</p>
              <div *ngIf="selectedPost!.tags && selectedPost!.tags.trim().length > 0" class="mt-2">
                <span *ngFor="let tag of getTagsArray(selectedPost!.tags)"
                      class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mr-1 mb-1">
                  {{ tag }}
                </span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                [(ngModel)]="selectedPost!.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                <option value="Published">Published</option>
                <option value="Draft">Draft</option>
                <option value="Archived">Archived</option>
              </select>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            (click)="saveBlogPost()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm">
            {{ modalType === 'add' ? 'Add' : 'Save' }}
          </button>
          <button
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div *ngIf="showModal && modalType === 'delete'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <span class="material-icons text-red-600">warning</span>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Delete Blog Post
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Are you sure you want to delete "{{ selectedPost?.title }}"? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            (click)="deleteBlogPost()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
          <button
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
