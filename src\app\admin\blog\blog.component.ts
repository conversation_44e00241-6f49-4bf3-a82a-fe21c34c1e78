import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Blog, BlogServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';


@Component({
  selector: 'app-blog',
  standalone: true,
  imports: [CommonModule, FormsModule,ServiceProxyModule],
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.css']
})
export class BlogComponent implements OnInit {
  blogPosts: Blog[] = [];
  selectedPost: Blog | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  tagsInput = ''; // For handling tags input as comma-separated string

  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Art', 'Culture', 'Events', 'Artists', 'Exhibitions'];

  constructor(private blogService: BlogServiceProxy) {}

  ngOnInit(): void {
    this.loadBlogPosts();
  }

  loadBlogPosts(): void {
    this.isLoading = true;
    this.blogService.getAllBlogs().subscribe({
      next: (posts) => {
        console.log('Posts:', posts);
        this.blogPosts = posts;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading blog posts', error);
        this.isLoading = false;
      }
    });
  }

  openAddModal(): void {
    this.selectedPost = new Blog();
    this.selectedPost.tags = []; // Initialize empty tags array
    this.tagsInput = ''; // Clear tags input
    this.modalType = 'add';
    this.showModal = true;
  }

  openEditModal(post: Blog): void {
    this.selectedPost = Object.assign(new Blog(), post);
    // Convert tags array to comma-separated string for editing
    this.tagsInput = this.selectedPost.tags ? this.selectedPost.tags.join(', ') : '';
    this.modalType = 'edit';
    this.showModal = true;
  }

  openDeleteModal(post: Blog): void {
    this.selectedPost = post;
    this.modalType = 'delete';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedPost = null;
  }

  saveBlogPost(): void {
    if (!this.selectedPost) return;

    // Validate required fields
    if (!this.selectedPost.title || !this.selectedPost.author || !this.selectedPost.category) {
      alert('Please fill in all required fields (Title, Author, Category)');
      return;
    }

    // Validate tags
    if (!this.tagsInput || this.tagsInput.trim() === '') {
      alert('Please enter at least one tag');
      return;
    }

    // Convert comma-separated tags string to array
    this.selectedPost.tags = this.tagsInput
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    // Ensure all fields are properly set
    if (!this.selectedPost.title.trim() || !this.selectedPost.author.trim() || !this.selectedPost.category.trim()) {
      alert('Please fill in all required fields with valid values');
      return;
    }

    // Set id to 0 for new posts
    if (this.modalType === 'add') {
      this.selectedPost.id = 0;
    }

    console.log('Saving blog post:', this.selectedPost);

    // Create a proper Blog instance using the constructor
    const postToSave = new Blog({
      id: this.selectedPost.id,
      title: this.selectedPost.title,
      author: this.selectedPost.author,
      authorImageUrl: this.selectedPost.authorImageUrl,
      content: this.selectedPost.content,
      category: this.selectedPost.category,
      imageUrl: this.selectedPost.imageUrl,
      publishDate: this.selectedPost.publishDate,
      status: this.selectedPost.status,
      tags: this.selectedPost.tags
    });

    console.log('Blog object to save:', postToSave);
    console.log('Blog object JSON:', JSON.stringify(postToSave));

    if (this.modalType === 'add') {
      this.blogService.createBlogsCreateBlogs(postToSave).subscribe({
        next: (res) => {
          console.log('Blog post added successfully:', res);
          this.loadBlogPosts();
          this.closeModal();
        },
        error: (error) => {
          console.error('Error adding blog post:', error);
          this.handleSaveError(error);
        }
      });
    } else if (this.modalType === 'edit') {
      this.blogService.createBlogsCreateBlogs(postToSave).subscribe({
        next: (res) => {
          console.log('Blog post updated successfully:', res);
          this.loadBlogPosts();
          this.closeModal();
        },
        error: (error) => {
          console.error('Error updating blog post:', error);
          this.handleSaveError(error);
        }
      });
    }
  }

  private handleSaveError(error: any): void {
    // Handle validation errors
    if (error.status === 400 && error.error && error.error.errors) {
      console.log('Full error response:', error);
      let errorMessage = 'Validation errors:\n';
      for (const field in error.error.errors) {
        errorMessage += `${field}: ${error.error.errors[field].join(', ')}\n`;
      }
      alert(errorMessage);
    } else {
      console.log('Full error response:', error);
      alert('Failed to save blog post. Please try again.');
    }
  }

  deleteBlogPost(): void {
    if (!this.selectedPost) return;

    this.blogService.deleteBlog(this.selectedPost.id).subscribe({
      next: () => {
        this.loadBlogPosts();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting blog post', error)
    });
  }

  get filteredBlogPosts(): Blog[] {
    return this.blogPosts.filter(post => {
      const matchesSearch = this.searchTerm === '' ||
        post.title?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.content?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.author?.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = this.categoryFilter === '' ||
        post.category === this.categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }
}
