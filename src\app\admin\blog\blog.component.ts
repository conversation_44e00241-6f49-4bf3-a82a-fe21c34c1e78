import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BlogService } from '../services/blog.service';
import { BlogPost } from '../models/blog-post.model';

@Component({
  selector: 'app-blog',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.css']
})
export class BlogComponent implements OnInit {
  blogPosts: BlogPost[] = [];
  selectedPost: BlogPost | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  
  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Art', 'Culture', 'Events', 'Artists', 'Exhibitions'];
  
  constructor(private blogService: BlogService) {}

  ngOnInit(): void {
    this.loadBlogPosts();
  }
  
  loadBlogPosts(): void {
    this.isLoading = true;
    this.blogService.getBlogPosts().subscribe({
      next: (posts) => {
        this.blogPosts = posts;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading blog posts', error);
        this.isLoading = false;
      }
    });
  }
  
  openAddModal(): void {
    this.selectedPost = new BlogPost();
    this.modalType = 'add';
    this.showModal = true;
  }
  
  openEditModal(post: BlogPost): void {
    this.selectedPost = { ...post };
    this.modalType = 'edit';
    this.showModal = true;
  }
  
  openDeleteModal(post: BlogPost): void {
    this.selectedPost = post;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedPost = null;
  }
  
  saveBlogPost(): void {
    if (!this.selectedPost) return;
    
    if (this.modalType === 'add') {
      this.blogService.addBlogPost(this.selectedPost).subscribe({
        next: () => {
          this.loadBlogPosts();
          this.closeModal();
        },
        error: (error) => console.error('Error adding blog post', error)
      });
    } else if (this.modalType === 'edit') {
      this.blogService.updateBlogPost(this.selectedPost).subscribe({
        next: () => {
          this.loadBlogPosts();
          this.closeModal();
        },
        error: (error) => console.error('Error updating blog post', error)
      });
    }
  }
  
  deleteBlogPost(): void {
    if (!this.selectedPost) return;
    
    this.blogService.deleteBlogPost(this.selectedPost.id).subscribe({
      next: () => {
        this.loadBlogPosts();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting blog post', error)
    });
  }
  
  get filteredBlogPosts(): BlogPost[] {
    return this.blogPosts.filter(post => {
      const matchesSearch = this.searchTerm === '' || 
        post.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.content.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.author.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesCategory = this.categoryFilter === '' || 
        post.category === this.categoryFilter;
        
      return matchesSearch && matchesCategory;
    });
  }
}
