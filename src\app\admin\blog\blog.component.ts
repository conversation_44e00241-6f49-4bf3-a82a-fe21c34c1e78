import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Blog, BlogServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';


@Component({
  selector: 'app-blog',
  standalone: true,
  imports: [CommonModule, FormsModule,ServiceProxyModule],
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.css']
})
export class BlogComponent implements OnInit {
  blogPosts: Blog[] = [];
  selectedPost: Blog | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  tagsInput = ''; // For handling tags input as comma-separated string

  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Art', 'Culture', 'Events', 'Artists', 'Exhibitions'];

  constructor(private blogService: BlogServiceProxy) {}

  ngOnInit(): void {
    this.loadBlogPosts();
  }

  loadBlogPosts(): void {
    this.isLoading = true;
    this.blogService.getAllBlogs().subscribe({
      next: (posts) => {
        console.log('Posts:', posts);
        this.blogPosts = posts;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading blog posts', error);
        this.isLoading = false;
      }
    });
  }

  openAddModal(): void {
    this.selectedPost = new Blog();
    // Initialize with default values
    this.selectedPost.id = 0;
    this.selectedPost.title = '';
    this.selectedPost.author = '';
    this.selectedPost.authorImageUrl = '';
    this.selectedPost.content = '';
    this.selectedPost.category = '';
    this.selectedPost.imageUrl = '';
    this.selectedPost.publishDate = DateTime.now();
    this.selectedPost.status = 'Draft';
    this.selectedPost.tags = '';
    this.tagsInput = '';
    this.modalType = 'add';
    this.showModal = true;
  }

  openEditModal(post: Blog): void {
    // Create a new Blog instance and copy all properties
    this.selectedPost = new Blog();
    this.selectedPost.id = post.id;
    this.selectedPost.title = post.title;
    this.selectedPost.author = post.author;
    this.selectedPost.authorImageUrl = post.authorImageUrl;
    this.selectedPost.content = post.content;
    this.selectedPost.category = post.category;
    this.selectedPost.imageUrl = post.imageUrl;
    this.selectedPost.status = post.status;
    this.selectedPost.tags = post.tags;

    // Handle publishDate properly - always set to current time for updates
    this.selectedPost.publishDate = DateTime.now();

    // Set tags input from the tags string
    this.tagsInput = (typeof this.selectedPost.tags === 'string') ? this.selectedPost.tags : '';
    this.modalType = 'edit';
    this.showModal = true;
  }

  openDeleteModal(post: Blog): void {
    this.selectedPost = post;
    this.modalType = 'delete';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedPost = null;
  }

  saveBlogPost(): void {
    if (!this.selectedPost) return;

    // Validate required fields
    if (!this.selectedPost.title || !this.selectedPost.author || !this.selectedPost.category) {
      alert('Please fill in all required fields (Title, Author, Category)');
      return;
    }

    // Validate tags
    if (!this.tagsInput || this.tagsInput.trim() === '') {
      alert('Please enter at least one tag');
      return;
    }

    // Set tags as comma-separated string (as expected by backend)
    this.selectedPost.tags = this.tagsInput.trim();

    // Ensure all fields are properly set
    if (!this.selectedPost.title?.trim() || !this.selectedPost.author?.trim() || !this.selectedPost.category?.trim() || !this.selectedPost.tags?.trim()) {
      alert('Please fill in all required fields with valid values');
      return;
    }

    // Set proper ID based on operation type
    if (this.modalType === 'add') {
      this.selectedPost.id = 0; // Backend will create new blog
    }
    // For edit mode, keep the existing ID

    // Always ensure we have a valid publishDate
    const validPublishDate = DateTime.now();

    console.log('Saving blog post with current timestamp');

    // Create a clean Blog object with all required fields
    const postToSave = new Blog();
    postToSave.id = this.selectedPost.id;
    postToSave.title = this.selectedPost.title;
    postToSave.author = this.selectedPost.author;
    postToSave.authorImageUrl = this.selectedPost.authorImageUrl || '';
    postToSave.content = this.selectedPost.content;
    postToSave.category = this.selectedPost.category;
    postToSave.imageUrl = this.selectedPost.imageUrl || '';
    postToSave.publishDate = validPublishDate;
    postToSave.status = this.selectedPost.status || 'Draft';
    postToSave.tags = this.selectedPost.tags;

    console.log('Blog object to save:', postToSave);
    console.log('Blog object JSON:', JSON.stringify(postToSave));

    // Use the same method for both create and update since backend handles both
    this.blogService.createBlogsCreateBlogs(postToSave).subscribe({
      next: (res) => {
        const action = this.modalType === 'add' ? 'added' : 'updated';
        console.log(`Blog post ${action} successfully:`, res);
        this.loadBlogPosts();
        this.closeModal();

        // Show success message
        alert(`Blog post ${action} successfully!`);
      },
      error: (error) => {
        const action = this.modalType === 'add' ? 'adding' : 'updating';
        console.error(`Error ${action} blog post:`, error);
        this.handleSaveError(error);
      }
    });
  }

  private handleSaveError(error: any): void {
    console.error('Full error object:', error);

    // Handle different types of errors
    if (error.status === 400) {
      // Bad Request - Validation errors
      if (error.error && error.error.errors) {
        let errorMessage = 'Validation errors:\n';
        for (const field in error.error.errors) {
          errorMessage += `${field}: ${error.error.errors[field].join(', ')}\n`;
        }
        alert(errorMessage);
      } else {
        const errorText = error.error?.message || error.message || 'Please check all required fields are filled correctly';
        alert('Validation error: ' + errorText);
      }
    } else if (error.status === 404) {
      // Not Found - Blog doesn't exist for update
      alert('Blog post not found. It may have been deleted by another user.');
    } else if (error.status === 500) {
      // Server Error
      alert('Server error occurred. Please try again later.');
    } else {
      // Other errors
      const errorMessage = error.message || error.error?.message || 'An unexpected error occurred';
      alert(`Error: ${errorMessage}. Please try again.`);
    }
  }

  deleteBlogPost(): void {
    if (!this.selectedPost) return;

    this.blogService.deleteBlog(this.selectedPost.id).subscribe({
      next: () => {
        this.loadBlogPosts();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting blog post', error)
    });
  }

  get filteredBlogPosts(): Blog[] {
    return this.blogPosts.filter(post => {
      const matchesSearch = this.searchTerm === '' ||
        post.title?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.content?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        post.author?.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = this.categoryFilter === '' ||
        post.category === this.categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }

  getTagsArray(tagsString: string | undefined): string[] {
    if (!tagsString || !tagsString.trim()) {
      return [];
    }
    return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }
}
