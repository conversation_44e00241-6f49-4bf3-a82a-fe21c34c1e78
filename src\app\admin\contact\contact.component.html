<div class="container mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-semibold text-gray-700">Contact Messages</h2>
  </div>
  
  <!-- Filters -->
  <div class="mb-6 flex flex-col md:flex-row gap-4">
    <div class="flex-1">
      <label class="block text-sm text-gray-700 mb-2">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span class="material-icons text-gray-400 text-sm">search</span>
        </div>
        <input 
          type="text" 
          [(ngModel)]="searchTerm"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
          placeholder="Search by name, email, subject, or message">
      </div>
    </div>
    
    <div class="w-full md:w-64">
      <label class="block text-sm text-gray-700 mb-2">Status</label>
      <select 
        [(ngModel)]="statusFilter"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring focus:ring-purple-200 focus:ring-opacity-50 focus:border-purple-300">
        <option value="">All Statuses</option>
        <option *ngFor="let status of statuses" [value]="status">{{ status }}</option>
      </select>
    </div>
  </div>
  
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center my-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
  
  <!-- Messages Table -->
  <div *ngIf="!isLoading" class="overflow-x-auto bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            From
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Subject
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Date
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Status
          </th>
          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let message of filteredMessages" [ngClass]="{'bg-blue-50': !message.isRead}">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-full" [src]="'https://ui-avatars.com/api/?name=' + message.name" alt="">
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">{{ message.name }}</div>
                <div class="text-sm text-gray-500">{{ message.email }}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4">
            <div class="text-sm text-gray-900 font-medium" [ngClass]="{'font-bold': !message.isRead}">
              {{ message.subject }}
            </div>
            <div class="text-sm text-gray-500 truncate max-w-xs">{{ message.message }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ message.submittedAt | date }}</div>
            <div class="text-sm text-gray-500">{{ message.submittedAt | date:'shortTime' }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
              [ngClass]="{
                'bg-yellow-100 text-yellow-800': message.status === 'New',
                'bg-blue-100 text-blue-800': message.status === 'In Progress',
                'bg-green-100 text-green-800': message.status === 'Resolved'
              }">
              {{ message.status }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button 
              (click)="openViewModal(message)"
              class="text-indigo-600 hover:text-indigo-900 mr-3">
              <span class="material-icons text-sm">visibility</span>
            </button>
            <button 
              (click)="openReplyModal(message)"
              class="text-green-600 hover:text-green-900 mr-3">
              <span class="material-icons text-sm">reply</span>
            </button>
            <button 
              (click)="openDeleteModal(message)"
              class="text-red-600 hover:text-red-900">
              <span class="material-icons text-sm">delete</span>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <!-- Empty State -->
  <div *ngIf="!isLoading && filteredMessages.length === 0" class="text-center py-8">
    <span class="material-icons text-gray-400 text-5xl mb-4">mail</span>
    <h3 class="text-lg font-medium text-gray-600 mb-2">No messages found</h3>
    <p class="text-gray-500">Try adjusting your search or filters</p>
  </div>
  
  <!-- View Message Modal -->
  <div *ngIf="showModal && modalType === 'view'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-start">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Message Details
            </h3>
            <div class="flex space-x-2">
              <button 
                *ngFor="let status of statuses"
                (click)="updateStatus(selectedMessage!, status)"
                class="px-2 py-1 text-xs font-semibold rounded-full"
                [ngClass]="{
                  'bg-yellow-100 text-yellow-800': status === 'New',
                  'bg-blue-100 text-blue-800': status === 'In Progress',
                  'bg-green-100 text-green-800': status === 'Resolved',
                  'ring-2': selectedMessage?.status === status
                }">
                {{ status }}
              </button>
            </div>
          </div>
          
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-500">From</p>
              <div class="mt-1 flex items-center">
                <img class="h-10 w-10 rounded-full mr-3" [src]="'https://ui-avatars.com/api/?name=' + selectedMessage?.name" alt="">
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ selectedMessage?.name }}</p>
                  <p class="text-sm text-gray-500">{{ selectedMessage?.email }}</p>
                </div>
              </div>
            </div>
            
            <div>
              <p class="text-sm font-medium text-gray-500">Subject</p>
              <p class="mt-1 text-sm text-gray-900">{{ selectedMessage?.subject }}</p>
            </div>
            
            <div>
              <p class="text-sm font-medium text-gray-500">Message</p>
              <div class="mt-1 p-3 bg-gray-50 rounded-md">
                <p class="text-sm text-gray-900 whitespace-pre-line">{{ selectedMessage?.message }}</p>
              </div>
            </div>
            
            <div>
              <p class="text-sm font-medium text-gray-500">Date</p>
              <p class="mt-1 text-sm text-gray-900">{{ selectedMessage?.submittedAt | date:'medium' }}</p>
            </div>
            
            <div *ngIf="selectedMessage?.notes">
              <p class="text-sm font-medium text-gray-500">Notes & Replies</p>
              <div class="mt-1 p-3 bg-gray-50 rounded-md">
                <p class="text-sm text-gray-900 whitespace-pre-line">{{ selectedMessage?.notes }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="openReplyModal(selectedMessage!)"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm">
            Reply
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Reply Modal -->
  <div *ngIf="showModal && modalType === 'reply'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Reply to {{ selectedMessage?.name }}
          </h3>
          
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-500">Original Message</p>
              <div class="mt-1 p-3 bg-gray-50 rounded-md max-h-32 overflow-y-auto">
                <p class="text-sm text-gray-900">{{ selectedMessage?.message }}</p>
              </div>
            </div>
            
            <div>
              <label for="reply" class="block text-sm font-medium text-gray-700">Your Reply</label>
              <textarea 
                id="reply"
                [(ngModel)]="replyText"
                rows="6"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"></textarea>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="sendReply()"
            [disabled]="!replyText.trim()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm">
            Send Reply
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Confirmation Modal -->
  <div *ngIf="showModal && modalType === 'delete'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <span class="material-icons text-red-600">warning</span>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Delete Message
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Are you sure you want to delete this message from {{ selectedMessage?.name }}? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="deleteMessage()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
