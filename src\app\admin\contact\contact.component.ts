import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ContactService } from '../services/contact.service';
import { ContactMessage } from '../models/contact-message.model';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.css']
})
export class ContactComponent implements OnInit {
  messages: ContactMessage[] = [];
  selectedMessage: ContactMessage | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'view', 'reply', 'delete'
  replyText = '';
  
  // Filters
  searchTerm = '';
  statusFilter = '';
  statuses = ['New', 'In Progress', 'Resolved'];
  
  constructor(private contactService: ContactService) {}

  ngOnInit(): void {
    this.loadMessages();
  }
  
  loadMessages(): void {
    this.isLoading = true;
    this.contactService.getContactMessages().subscribe({
      next: (messages) => {
        this.messages = messages;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading messages', error);
        this.isLoading = false;
      }
    });
  }
  
  openViewModal(message: ContactMessage): void {
    this.selectedMessage = { ...message };
    this.modalType = 'view';
    this.showModal = true;
    
    // Mark as read if it's new
    if (!message.isRead) {
      this.markAsRead(message);
    }
  }
  
  openReplyModal(message: ContactMessage): void {
    this.selectedMessage = { ...message };
    this.modalType = 'reply';
    this.showModal = true;
    this.replyText = '';
    
    // Mark as read if it's new
    if (!message.isRead) {
      this.markAsRead(message);
    }
  }
  
  openDeleteModal(message: ContactMessage): void {
    this.selectedMessage = message;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedMessage = null;
    this.replyText = '';
  }
  
  markAsRead(message: ContactMessage): void {
    const updatedMessage = { ...message, isRead: true };
    
    this.contactService.updateContactMessage(updatedMessage).subscribe({
      next: () => {
        this.loadMessages();
      },
      error: (error) => console.error('Error updating message', error)
    });
  }
  
  updateStatus(message: ContactMessage, status: string): void {
    const updatedMessage = { ...message, status: status };
    
    this.contactService.updateContactMessage(updatedMessage).subscribe({
      next: () => {
        this.loadMessages();
      },
      error: (error) => console.error('Error updating message status', error)
    });
  }
  
  sendReply(): void {
    if (!this.selectedMessage || !this.replyText.trim()) return;
    
    const updatedMessage = { 
      ...this.selectedMessage, 
      status: 'Resolved',
      isRead: true,
      notes: this.selectedMessage.notes 
        ? `${this.selectedMessage.notes}\n\n${new Date().toLocaleString()}: ${this.replyText}`
        : `${new Date().toLocaleString()}: ${this.replyText}`
    };
    
    this.contactService.updateContactMessage(updatedMessage).subscribe({
      next: () => {
        this.loadMessages();
        this.closeModal();
      },
      error: (error) => console.error('Error sending reply', error)
    });
  }
  
  deleteMessage(): void {
    if (!this.selectedMessage) return;
    
    this.contactService.deleteContactMessage(this.selectedMessage.id).subscribe({
      next: () => {
        this.loadMessages();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting message', error)
    });
  }
  
  get filteredMessages(): ContactMessage[] {
    return this.messages.filter(message => {
      const matchesSearch = this.searchTerm === '' || 
        message.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        message.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        message.subject.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        message.message.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesStatus = this.statusFilter === '' || 
        message.status === this.statusFilter;
        
      return matchesSearch && matchesStatus;
    });
  }
}
