<div class="container mx-auto">
  <h2 class="my-6 text-2xl font-semibold text-gray-700">
    Dashboard
  </h2>
  
  <!-- Stats Cards -->
  <div class="grid gap-6 mb-8 md:grid-cols-2 xl:grid-cols-4">
    <div *ngFor="let stat of stats" 
      class="flex items-center p-4 bg-white rounded-lg shadow-md">
      <div class="p-3 mr-4 text-white rounded-full" [ngClass]="stat.color">
        <span class="material-icons">{{ stat.icon }}</span>
      </div>
      <div>
        <p class="mb-2 text-sm font-medium text-gray-600">
          {{ stat.title }}
        </p>
        <p class="text-lg font-semibold text-gray-700">
          {{ stat.value }}
        </p>
      </div>
    </div>
  </div>
  
  <!-- Charts -->
  <div class="grid gap-6 mb-8 md:grid-cols-2">
    <div class="min-w-0 p-4 bg-white rounded-lg shadow-md">
      <h4 class="mb-4 font-semibold text-gray-800">
        Sales Overview
      </h4>
      <div class="h-64 bg-gray-100 flex items-center justify-center">
        <p class="text-gray-500">Sales Chart Placeholder</p>
      </div>
    </div>
    
    <div class="min-w-0 p-4 bg-white rounded-lg shadow-md">
      <h4 class="mb-4 font-semibold text-gray-800">
        Traffic Overview
      </h4>
      <div class="h-64 bg-gray-100 flex items-center justify-center">
        <p class="text-gray-500">Traffic Chart Placeholder</p>
      </div>
    </div>
  </div>
  
  <!-- Recent Activities -->
  <div class="w-full mb-8 overflow-hidden rounded-lg shadow-md">
    <div class="w-full overflow-x-auto">
      <table class="w-full whitespace-no-wrap">
        <thead>
          <tr class="text-xs font-semibold tracking-wide text-left text-gray-500 uppercase border-b bg-gray-50">
            <th class="px-4 py-3">Activity</th>
            <th class="px-4 py-3">Type</th>
            <th class="px-4 py-3">Date</th>
            <th class="px-4 py-3">Status</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y">
          <tr *ngFor="let activity of recentActivities" class="text-gray-700">
            <td class="px-4 py-3">
              <div class="flex items-center text-sm">
                <div>
                  <p class="font-semibold">{{ activity.title }}</p>
                </div>
              </div>
            </td>
            <td class="px-4 py-3 text-sm capitalize">
              {{ activity.type }}
            </td>
            <td class="px-4 py-3 text-sm">
              {{ activity.date | date:'medium' }}
            </td>
            <td class="px-4 py-3 text-xs">
              <span class="px-2 py-1 font-semibold leading-tight rounded-full"
                [ngClass]="{
                  'text-green-700 bg-green-100': activity.status === 'completed',
                  'text-yellow-700 bg-yellow-100': activity.status === 'pending',
                  'text-blue-700 bg-blue-100': activity.status === 'new'
                }">
                {{ activity.status }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
