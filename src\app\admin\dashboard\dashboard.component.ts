import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface StatCard {
  title: string;
  value: number;
  icon: string;
  color: string;
}

interface RecentActivity {
  id: number;
  type: string;
  title: string;
  date: Date;
  status: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  stats: StatCard[] = [
    { title: 'Total Artworks', value: 124, icon: 'photo_library', color: 'bg-blue-500' },
    { title: 'Total Products', value: 87, icon: 'shopping_cart', color: 'bg-green-500' },
    { title: 'Total Artists', value: 32, icon: 'people', color: 'bg-purple-500' },
    { title: 'Total Sales', value: 45, icon: 'attach_money', color: 'bg-yellow-500' }
  ];
  
  recentActivities: RecentActivity[] = [
    { id: 1, type: 'order', title: 'New order #1234', date: new Date(2023, 6, 15), status: 'pending' },
    { id: 2, type: 'artwork', title: 'New artwork uploaded', date: new Date(2023, 6, 14), status: 'completed' },
    { id: 3, type: 'contact', title: 'New contact message', date: new Date(2023, 6, 13), status: 'new' },
    { id: 4, type: 'blog', title: 'New blog post published', date: new Date(2023, 6, 12), status: 'completed' },
    { id: 5, type: 'artist', title: 'New artist registered', date: new Date(2023, 6, 11), status: 'pending' }
  ];
  
  constructor() {}

  ngOnInit(): void {
    // In a real app, we would fetch data from a service
  }
}
