<div class="container mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-semibold text-gray-700">Events Management</h2>
    <button 
      (click)="openAddModal()"
      class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-md active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
      <span class="material-icons align-middle mr-1 text-sm">add</span>
      Add Event
    </button>
  </div>
  
  <!-- Filters -->
  <div class="mb-6 flex flex-col md:flex-row gap-4">
    <div class="flex-1">
      <label class="block text-sm text-gray-700 mb-2">Search</label>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <span class="material-icons text-gray-400 text-sm">search</span>
        </div>
        <input 
          type="text" 
          [(ngModel)]="searchTerm"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
          placeholder="Search by title, description, or location">
      </div>
    </div>
    
    <div class="w-full md:w-64">
      <label class="block text-sm text-gray-700 mb-2">Status</label>
      <select 
        [(ngModel)]="statusFilter"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring focus:ring-purple-200 focus:ring-opacity-50 focus:border-purple-300">
        <option value="">All Statuses</option>
        <option *ngFor="let status of statuses" [value]="status">{{ status }}</option>
      </select>
    </div>
  </div>
  
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center my-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
  
  <!-- Events Table -->
  <div *ngIf="!isLoading" class="overflow-x-auto bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Event
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Date & Location
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Attendees
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Status
          </th>
          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let event of filteredEvents">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-md object-cover" [src]="event.imageUrl" alt="">
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">{{ event.title }}</div>
                <div class="text-xs text-gray-500 truncate max-w-xs">{{ event.description }}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4">
            <div class="text-sm text-gray-900">{{ formatDate(event.startDate) }}</div>
            <div *ngIf="event.endDate" class="text-xs text-gray-500">to {{ formatDate(event.endDate) }}</div>
            <div class="text-xs text-gray-500 mt-1">{{ event.location }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ event.currentAttendees }}</div>
            <div *ngIf="event.maxAttendees" class="text-xs text-gray-500">of {{ event.maxAttendees }}</div>
            <div *ngIf="!event.maxAttendees" class="text-xs text-gray-500">Unlimited</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
              [ngClass]="getStatusClass(event.status)">
              {{ event.status }}
            </span>
            <div *ngIf="event.isFeatured" class="mt-1">
              <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                Featured
              </span>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button 
              (click)="openEditModal(event)"
              class="text-indigo-600 hover:text-indigo-900 mr-3">
              <span class="material-icons text-sm">edit</span>
            </button>
            <button 
              (click)="openDeleteModal(event)"
              class="text-red-600 hover:text-red-900">
              <span class="material-icons text-sm">delete</span>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <!-- Empty State -->
  <div *ngIf="!isLoading && filteredEvents.length === 0" class="text-center py-8">
    <span class="material-icons text-gray-400 text-5xl mb-4">event</span>
    <h3 class="text-lg font-medium text-gray-600 mb-2">No events found</h3>
    <p class="text-gray-500">Try adjusting your search or filters</p>
  </div>
  
  <!-- Add/Edit Modal -->
  <div *ngIf="showModal && (modalType === 'add' || modalType === 'edit')" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ modalType === 'add' ? 'Add New Event' : 'Edit Event' }}
          </h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
              <input 
                type="text" 
                [(ngModel)]="selectedEvent!.title"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea 
                [(ngModel)]="selectedEvent!.description"
                rows="3"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input 
                  type="datetime-local" 
                  [(ngModel)]="selectedEvent!.startDate"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input 
                  type="datetime-local" 
                  [(ngModel)]="selectedEvent!.endDate"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <input 
                type="text" 
                [(ngModel)]="selectedEvent!.location"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
              <input 
                type="text" 
                [(ngModel)]="selectedEvent!.imageUrl"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Organizer</label>
              <input 
                type="text" 
                [(ngModel)]="selectedEvent!.organizer"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                <input 
                  type="email" 
                  [(ngModel)]="selectedEvent!.contactEmail"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                <input 
                  type="text" 
                  [(ngModel)]="selectedEvent!.contactPhone"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Registration URL</label>
              <input 
                type="text" 
                [(ngModel)]="selectedEvent!.registrationUrl"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Max Attendees</label>
                <input 
                  type="number" 
                  [(ngModel)]="selectedEvent!.maxAttendees"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Current Attendees</label>
                <input 
                  type="number" 
                  [(ngModel)]="selectedEvent!.currentAttendees"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select 
                [(ngModel)]="selectedEvent!.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                <option *ngFor="let status of statuses" [value]="status">{{ status }}</option>
              </select>
            </div>
            
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="featured" 
                [(ngModel)]="selectedEvent!.isFeatured"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
              <label for="featured" class="ml-2 block text-sm text-gray-900">
                Featured Event
              </label>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="saveEvent()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm">
            {{ modalType === 'add' ? 'Add' : 'Save' }}
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Confirmation Modal -->
  <div *ngIf="showModal && modalType === 'delete'" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" (click)="closeModal()">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <span class="material-icons text-red-600">warning</span>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Delete Event
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Are you sure you want to delete "{{ selectedEvent?.title }}"? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            (click)="deleteEvent()"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
          </button>
          <button 
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
