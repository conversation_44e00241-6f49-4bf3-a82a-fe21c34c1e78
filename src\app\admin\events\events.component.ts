import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EventsService } from '../services/events.service';
import { Event } from '../models/event.model';

@Component({
  selector: 'app-events',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './events.component.html',
  styleUrls: ['./events.component.css']
})
export class EventsComponent implements OnInit {
  events: Event[] = [];
  selectedEvent: Event | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  
  // Filters
  searchTerm = '';
  statusFilter = '';
  statuses = ['Upcoming', 'Ongoing', 'Completed', 'Cancelled'];
  
  constructor(private eventsService: EventsService) {}

  ngOnInit(): void {
    this.loadEvents();
  }
  
  loadEvents(): void {
    this.isLoading = true;
    this.eventsService.getEvents().subscribe({
      next: (events) => {
        this.events = events;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading events', error);
        this.isLoading = false;
      }
    });
  }
  
  openAddModal(): void {
    this.selectedEvent = new Event();
    this.modalType = 'add';
    this.showModal = true;
  }
  
  openEditModal(event: Event): void {
    this.selectedEvent = { ...event };
    this.modalType = 'edit';
    this.showModal = true;
  }
  
  openDeleteModal(event: Event): void {
    this.selectedEvent = event;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedEvent = null;
  }
  
  saveEvent(): void {
    if (!this.selectedEvent) return;
    
    if (this.modalType === 'add') {
      this.eventsService.addEvent(this.selectedEvent).subscribe({
        next: () => {
          this.loadEvents();
          this.closeModal();
        },
        error: (error) => console.error('Error adding event', error)
      });
    } else if (this.modalType === 'edit') {
      this.eventsService.updateEvent(this.selectedEvent).subscribe({
        next: () => {
          this.loadEvents();
          this.closeModal();
        },
        error: (error) => console.error('Error updating event', error)
      });
    }
  }
  
  deleteEvent(): void {
    if (!this.selectedEvent) return;
    
    this.eventsService.deleteEvent(this.selectedEvent.id).subscribe({
      next: () => {
        this.loadEvents();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting event', error)
    });
  }
  
  get filteredEvents(): Event[] {
    return this.events.filter(event => {
      const matchesSearch = this.searchTerm === '' || 
        event.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        event.location.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesStatus = this.statusFilter === '' || 
        event.status === this.statusFilter;
        
      return matchesSearch && matchesStatus;
    });
  }
  
  formatDate(date: Date | null): string {
    if (!date) return '';
    return new Date(date).toLocaleString();
  }
  
  getStatusClass(status: string): string {
    switch (status) {
      case 'Upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'Ongoing':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-gray-100 text-gray-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
