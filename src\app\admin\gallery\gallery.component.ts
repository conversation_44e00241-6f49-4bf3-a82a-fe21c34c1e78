import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GalleryService } from '../services/gallery.service';
import { Artwork } from '../models/artwork.model';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import {
  Gallery,
  GalleryServiceProxy,
} from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule],
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.css'],
})
export class GalleryComponent implements OnInit {
  artworks: Gallery[] = [];
  selectedArtwork: Gallery | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'

  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = [
    'Painting',
    'Sculpture',
    'Digital Art',
    'Photography',
    'Mixed Media',
  ];

  constructor(private galleryService: GalleryServiceProxy) {}

  ngOnInit(): void {
    this.loadArtworks();
  }

  loadArtworks(): void {
    this.isLoading = true;
    this.galleryService.getAllGalleries().subscribe({
      next: (artworks) => {
        this.artworks = artworks;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading artworks', error);
        this.isLoading = false;
      },
    });
  }

  openAddModal(): void {
    this.selectedArtwork = new Gallery();
    this.modalType = 'add';
    this.showModal = true;
  }

  openEditModal(artwork: Gallery): void {
    this.selectedArtwork = Object.assign(new Gallery(), artwork);
    this.modalType = 'edit';
    this.showModal = true;
  }

  openDeleteModal(artwork: Gallery): void {
    this.selectedArtwork = artwork;
    this.modalType = 'delete';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedArtwork = null;
  }

  saveArtwork(): void {
    if (!this.selectedArtwork) return;
    console.log(this.selectedArtwork);

    this.galleryService.addOrEditGallery(this.selectedArtwork).subscribe((res)=>{
      console.log(res)
    })


  }

  deleteArtwork(): void {
    if (!this.selectedArtwork) return;

    this.galleryService.deleteGallery(this.selectedArtwork.id).subscribe({
      next: () => {
        this.loadArtworks();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting artwork', error),
    });
  }

  get filteredArtworks(): Gallery[] {
    return this.artworks.filter((artwork) => {
      const matchesSearch =
        this.searchTerm === '' ||
        artwork.title?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artwork.artist?.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory =
        this.categoryFilter === '' || artwork.category === this.categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }
}
