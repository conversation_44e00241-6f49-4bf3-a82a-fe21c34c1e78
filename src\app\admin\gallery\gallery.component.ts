import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GalleryService } from '../services/gallery.service';
import { Artwork } from '../models/artwork.model';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import {
  Gallery,
  GalleryServiceProxy,
} from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule],
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.css'],
})
export class GalleryComponent implements OnInit {
  artworks: Gallery[] = [];
  selectedArtwork: Gallery | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'

  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = [
    'Painting',
    'Sculpture',
    'Digital Art',
    'Photography',
    'Mixed Media',
  ];

  constructor(private galleryService: GalleryServiceProxy) {}

  ngOnInit(): void {
    this.loadArtworks();
  }

  loadArtworks(): void {
    this.isLoading = true;
    this.galleryService.getAllGalleries().subscribe({
      next: (artworks) => {
        this.artworks = artworks;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading artworks', error);
        this.isLoading = false;
      },
    });
  }

  openAddModal(): void {
    this.selectedArtwork = new Gallery();
    this.modalType = 'add';
    this.showModal = true;
  }

  openEditModal(artwork: Gallery): void {
    this.selectedArtwork = Object.assign(new Gallery(), artwork);
    this.modalType = 'edit';
    this.showModal = true;
  }

  openDeleteModal(artwork: Gallery): void {
    this.selectedArtwork = artwork;
    this.modalType = 'delete';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedArtwork = null;
  }

  saveArtwork(): void {
    if (!this.selectedArtwork) return;

    // Validate required fields
    if (!this.selectedArtwork.title || !this.selectedArtwork.artist || !this.selectedArtwork.category) {
      alert('Please fill in all required fields (Title, Artist, Category)');
      return;
    }

    // Ensure all fields are properly set (not undefined)
    if (!this.selectedArtwork.title.trim() || !this.selectedArtwork.artist.trim() || !this.selectedArtwork.category.trim()) {
      alert('Please fill in all required fields with valid values');
      return;
    }

    // Ensure price is a string
    if (this.selectedArtwork.price) {
      this.selectedArtwork.price = this.selectedArtwork.price.toString();
    }

    // Set id to 0 for new artworks
    if (this.modalType === 'add') {
      this.selectedArtwork.id = 0;
    }

    console.log('Saving artwork:', this.selectedArtwork);

    // Create a proper Gallery instance using the constructor
    const artworkToSave = new Gallery({
      id: this.selectedArtwork.id,
      title: this.selectedArtwork.title,
      artist: this.selectedArtwork.artist,
      category: this.selectedArtwork.category,
      description: this.selectedArtwork.description,
      imageUrl: this.selectedArtwork.imageUrl,
      price: this.selectedArtwork.price
    });

    console.log('Gallery object to save:', artworkToSave);
    console.log('Gallery object JSON:', JSON.stringify(artworkToSave));

    this.galleryService.addOrEditGallery(artworkToSave).subscribe({
      next: (res) => {
        console.log('Artwork saved successfully:', res);
        this.loadArtworks();
        this.closeModal();
      },
      error: (error) => {
        console.error('Error saving artwork:', error);

        // Handle validation errors
        if (error.status === 400 && error.error && error.error.errors) {
          console.log('Full error response:', error);
          let errorMessage = 'Validation errors:\n';
          for (const field in error.error.errors) {
            errorMessage += `${field}: ${error.error.errors[field].join(', ')}\n`;
          }
          alert(errorMessage);
        } else {
          console.log('Full error response:', error);
          alert('Failed to save artwork. Please try again.');
        }
      }
    });
  }

  deleteArtwork(): void {
    if (!this.selectedArtwork) return;

    this.galleryService.deleteGallery(this.selectedArtwork.id).subscribe({
      next: () => {
        this.loadArtworks();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting artwork', error),
    });
  }

  get filteredArtworks(): Gallery[] {
    return this.artworks.filter((artwork) => {
      const matchesSearch =
        this.searchTerm === '' ||
        artwork.title?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artwork.artist?.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory =
        this.categoryFilter === '' || artwork.category === this.categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }
}
