import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GalleryService } from '../services/gallery.service';
import { Artwork } from '../models/artwork.model';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.css']
})
export class GalleryComponent implements OnInit {
  artworks: Artwork[] = [];
  selectedArtwork: Artwork | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  
  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Painting', 'Sculpture', 'Digital Art', 'Photography', 'Mixed Media'];
  
  constructor(private galleryService: GalleryService) {}

  ngOnInit(): void {
    this.loadArtworks();
  }
  
  loadArtworks(): void {
    this.isLoading = true;
    this.galleryService.getArtworks().subscribe({
      next: (artworks) => {
        this.artworks = artworks;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading artworks', error);
        this.isLoading = false;
      }
    });
  }
  
  openAddModal(): void {
    this.selectedArtwork = new Artwork();
    this.modalType = 'add';
    this.showModal = true;
  }
  
  openEditModal(artwork: Artwork): void {
    this.selectedArtwork = { ...artwork };
    this.modalType = 'edit';
    this.showModal = true;
  }
  
  openDeleteModal(artwork: Artwork): void {
    this.selectedArtwork = artwork;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedArtwork = null;
  }
  
  saveArtwork(): void {
    if (!this.selectedArtwork) return;
    
    if (this.modalType === 'add') {
      this.galleryService.addArtwork(this.selectedArtwork).subscribe({
        next: () => {
          this.loadArtworks();
          this.closeModal();
        },
        error: (error) => console.error('Error adding artwork', error)
      });
    } else if (this.modalType === 'edit') {
      this.galleryService.updateArtwork(this.selectedArtwork).subscribe({
        next: () => {
          this.loadArtworks();
          this.closeModal();
        },
        error: (error) => console.error('Error updating artwork', error)
      });
    }
  }
  
  deleteArtwork(): void {
    if (!this.selectedArtwork) return;
    
    this.galleryService.deleteArtwork(this.selectedArtwork.id).subscribe({
      next: () => {
        this.loadArtworks();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting artwork', error)
    });
  }
  
  get filteredArtworks(): Artwork[] {
    return this.artworks.filter(artwork => {
      const matchesSearch = this.searchTerm === '' || 
        artwork.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        artwork.artist.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesCategory = this.categoryFilter === '' || 
        artwork.category === this.categoryFilter;
        
      return matchesSearch && matchesCategory;
    });
  }
}
