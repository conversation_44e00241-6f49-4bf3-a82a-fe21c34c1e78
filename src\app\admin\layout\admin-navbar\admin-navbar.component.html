<!-- Sidebar Container - Full height with fixed width -->
<div class="fixed inset-y-0 left-0 z-40 flex flex-col w-64 h-screen bg-white border-r shadow-sm" id="sidebar">
  <!-- Header with Logo -->
  <div class="p-4 border-b">
    <h1 class="text-xl font-semibold text-gray-800 font-display"><PERSON><PERSON><PERSON></h1>
  </div>

  <!-- Navigation Menu - Takes most of the height -->
  <nav class="flex-1 py-2">
    <!-- Dashboard -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
        </svg>
      </span>
      <span class="ml-3">Dashboard</span>
    </a>

    <!-- Gallery - Active state example -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
        </svg>
      </span>
      <span class="ml-3">Gallery</span>
    </a>

    <!-- Shop -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
        </svg>
      </span>
      <span class="ml-3">Shop</span>
    </a>

    <!-- Blog -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clip-rule="evenodd" />
          <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V7z" />
        </svg>
      </span>
      <span class="ml-3">Blog</span>
    </a>

    <!-- Artists -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      </span>
      <span class="ml-3">Artists</span>
    </a>

    <!-- Contact Messages -->
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
        </svg>
      </span>
      <span class="ml-3">Contact Messages</span>
    </a>
  </nav>

  <!-- Divider before logout -->
  <div class="border-t"></div>

  <!-- Logout Button (at bottom) -->
  <div class="py-2">
    <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 transition-colors">
      <span class="flex items-center justify-center w-6 h-6 text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm7 5a1 1 0 10-2 0v4a1 1 0 102 0V8zm-1 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
          <path d="M11 3.5a.5.5 0 01.5-.5h4a.5.5 0 01.5.5v4a.5.5 0 01-1 0V4.5H12a.5.5 0 01-.5-.5z" />
        </svg>
      </span>
      <span class="ml-3">Logout</span>
    </a>
  </div>
</div>

<!-- Mobile toggle button -->
<button class="fixed bottom-4 right-4 p-3 rounded-full bg-gray-800 text-white shadow-lg md:hidden" id="sidebar-mobile-toggle">
  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
  </svg>
</button>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const mobileToggle = document.getElementById('sidebar-mobile-toggle');

    mobileToggle.addEventListener('click', function() {
      sidebar.classList.toggle('-translate-x-full');
    });

    // Initialize for mobile
    if (window.innerWidth < 768) {
      sidebar.classList.add('-translate-x-full');
    }
  });
</script>

