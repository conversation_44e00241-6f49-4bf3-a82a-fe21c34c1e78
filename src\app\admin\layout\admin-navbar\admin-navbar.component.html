<header class="z-10 py-4 bg-white shadow-md">
  <div class="container flex items-center justify-between h-full px-6 mx-auto">
    <!-- Mobile hamburger -->
    <button
      class="p-1 mr-5 -ml-1 rounded-md md:hidden focus:outline-none focus:shadow-outline-purple"
      (click)="toggleSidebar.emit()">
      <span class="material-icons">menu</span>
    </button>

    <!-- Search input -->
    <div class="flex justify-center flex-1 lg:mr-32">
      <div class="relative w-full max-w-xl mr-6 focus-within:text-purple-500">
        <div class="absolute inset-y-0 flex items-center pl-2">
          <span class="material-icons text-gray-400">search</span>
        </div>
        <input
          class="w-full pl-8 pr-2 py-2 text-sm text-gray-700 placeholder-gray-600 bg-gray-100 border-0 rounded-md focus:placeholder-gray-500 focus:bg-white focus:border-purple-300 focus:outline-none form-input"
          type="text"
          placeholder="Search for items"
          aria-label="Search"
        />
      </div>
    </div>

    <ul class="flex items-center flex-shrink-0 space-x-6">
      <!-- Notifications menu -->
      <li class="relative">
        <button
          class="relative align-middle rounded-md focus:outline-none focus:shadow-outline-purple">
          <span class="material-icons">notifications</span>
          <!-- Notification badge -->
          <span
            class="absolute top-0 right-0 inline-block w-3 h-3 transform translate-x-1 -translate-y-1 bg-red-600 border-2 border-white rounded-full">
          </span>
        </button>
      </li>

      <!-- Profile menu -->
      <li class="relative">
        <button
          class="align-middle rounded-full focus:shadow-outline-purple focus:outline-none"
          (click)="toggleDropdown()">
          <img
            class="object-cover w-8 h-8 rounded-full"
            src="https://ui-avatars.com/api/?name=Admin&background=6366F1&color=fff"
            alt=""
            aria-hidden="true"
          />
        </button>

        <div
          *ngIf="dropdownOpen"
          class="absolute right-0 w-56 p-2 mt-2 space-y-2 text-gray-600 bg-white border border-gray-100 rounded-md shadow-md">
          <div class="px-4 py-2">
            <p class="text-sm font-medium text-gray-800" *ngIf="authService.currentUserValue">
              {{ authService.currentUserValue.username }}
            </p>
            <p class="text-xs text-gray-500" *ngIf="authService.currentUserValue">
              {{ authService.currentUserValue.email }}
            </p>
          </div>
          <hr class="border-gray-100">
          <a
            routerLink="/admin/profile"
            class="block px-4 py-2 text-sm transition-colors duration-150 hover:bg-gray-100 rounded-md">
            <span class="material-icons text-sm mr-2 align-middle">person</span>
            Profile
          </a>
          <a
            routerLink="/admin/settings"
            class="block px-4 py-2 text-sm transition-colors duration-150 hover:bg-gray-100 rounded-md">
            <span class="material-icons text-sm mr-2 align-middle">settings</span>
            Settings
          </a>
          <button
            (click)="logout()"
            class="block w-full text-left px-4 py-2 text-sm transition-colors duration-150 hover:bg-gray-100 rounded-md">
            <span class="material-icons text-sm mr-2 align-middle">logout</span>
            Logout
          </button>
        </div>
      </li>
    </ul>
  </div>
</header>
