<!-- Sidebar backdrop (mobile only) -->
<div
  *ngIf="isOpen"
  class="fixed inset-0 z-20 transition-opacity bg-black opacity-50 lg:hidden"
  (click)="closeSidebar.emit()">
</div>

<!-- Sidebar -->
<aside
  class="fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto transition duration-300 transform bg-white border-r lg:translate-x-0 lg:static lg:inset-0"
  [ngClass]="isOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in'" style="min-height: calc(100vh - 64px);">

  <div class="flex items-center justify-between p-4 border-b">
    <div class="flex items-center">
      <span class="text-xl font-semibold text-gray-800"><PERSON><PERSON><PERSON></span>
    </div>
    <button
      class="p-1 text-gray-600 rounded-md lg:hidden hover:text-gray-900 hover:bg-gray-100"
      (click)="closeSidebar.emit()">
      <span class="material-icons">close</span>
    </button>
  </div>

  <nav class="mt-5 px-2">
    <div class="space-y-1">
      <a *ngFor="let item of navItems"
        [routerLink]="item.route"
        routerLinkActive="bg-gray-100 text-gray-900"
        class="flex items-center px-4 py-3 text-gray-600 transition-colors duration-200 rounded-md hover:bg-gray-100 hover:text-gray-900"
        (click)="closeOnMobile()">
        <span class="material-icons mr-3">{{ item.icon }}</span>
        <span>{{ item.label }}</span>
      </a>
    </div>
  </nav>

  <div class="absolute bottom-0 w-full p-4 border-t">
    <button
      (click)="logout()"
      class="flex items-center w-full px-4 py-2 text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900">
      <span class="material-icons mr-3">logout</span>
      <span>Logout</span>
    </button>
  </div>
</aside>
