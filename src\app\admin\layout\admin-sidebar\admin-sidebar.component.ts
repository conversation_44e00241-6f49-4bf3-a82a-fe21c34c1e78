import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';

interface NavItem {
  label: string;
  icon: string;
  route: string;
}

@Component({
  selector: 'app-admin-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './admin-sidebar.component.html',
  styleUrls: ['./admin-sidebar.component.css']
})
export class AdminSidebarComponent {
  @Input() isOpen = true;
  @Output() closeSidebar = new EventEmitter<void>();

  navItems: NavItem[] = [
    { label: 'Dashboard', icon: 'home', route: '/admin/dashboard' },
    { label: 'Gallery', icon: 'photo_library', route: '/admin/gallery' },
    { label: 'Shop', icon: 'shopping_cart', route: '/admin/shop' },
    { label: 'Blog', icon: 'article', route: '/admin/blog' },
    { label: 'Artists', icon: 'people', route: '/admin/artists' },
    { label: 'Events', icon: 'event', route: '/admin/events' },
    { label: 'Contact Messages', icon: 'mail', route: '/admin/contact' }
  ];

  constructor(private authService: AuthService) {}

  logout(): void {
    this.authService.logout();
  }

  closeOnMobile(): void {
    if (window.innerWidth < 768) {
      this.closeSidebar.emit();
    }
  }
}
