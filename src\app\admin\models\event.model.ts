export class Event {
  id: number = 0;
  title: string = '';
  description: string = '';
  startDate: Date = new Date();
  endDate: Date | null = null;
  location: string = '';
  imageUrl: string = '';
  organizer: string = '';
  contactEmail: string = '';
  contactPhone: string = '';
  registrationUrl: string = '';
  maxAttendees: number | null = null;
  currentAttendees: number = 0;
  status: string = 'Upcoming'; // 'Upcoming', 'Ongoing', 'Completed', 'Cancelled'
  isFeatured: boolean = false;
  createdAt?: Date;
}
