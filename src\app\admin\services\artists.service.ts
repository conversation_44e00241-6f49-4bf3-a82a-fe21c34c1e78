import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Artist } from '../models/artist.model';

@Injectable({
  providedIn: 'root'
})
export class ArtistsService {
  private apiUrl = 'api/artists';
  
  // Mock data for demo purposes
  private mockArtists: Artist[] = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      location: 'Madhubani, Bihar',
      specialty: 'Painting',
      bio: '<PERSON><PERSON> is a renowned Mithila artist with over 20 years of experience. Her work has been exhibited internationally and she has received numerous awards for her contributions to preserving and promoting Mithila art.',
      profileImage: 'https://randomuser.me/api/portraits/women/32.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      featured: true,
      artworks: 45,
      joinedDate: new Date('2018-03-15')
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      location: 'Patna, Bihar',
      specialty: 'Sculpture',
      bio: '<PERSON><PERSON> specializes in creating intricate sculptures inspired by Mithila art traditions. His work combines traditional motifs with contemporary themes, creating a unique artistic expression.',
      profileImage: 'https://randomuser.me/api/portraits/men/45.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543211',
      featured: false,
      artworks: 28,
      joinedDate: new Date('2019-06-22')
    },
    {
      id: 3,
      name: 'Priya Singh',
      location: 'Delhi',
      specialty: 'Digital Art',
      bio: 'Priya Singh is a digital artist who brings Mithila art into the digital realm. Her innovative approach combines traditional Mithila patterns with modern digital techniques, appealing to a younger audience.',
      profileImage: 'https://randomuser.me/api/portraits/women/68.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      featured: true,
      artworks: 36,
      joinedDate: new Date('2020-01-10')
    },
    {
      id: 4,
      name: 'Amit Sharma',
      location: 'Mumbai, Maharashtra',
      specialty: 'Photography',
      bio: 'Amit Sharma is a photographer who documents Mithila art and artists. His photographic work captures the essence of the art form and the people behind it, telling stories through his lens.',
      profileImage: 'https://randomuser.me/api/portraits/men/22.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543213',
      featured: false,
      artworks: 52,
      joinedDate: new Date('2019-11-05')
    },
    {
      id: 5,
      name: 'Neha Gupta',
      location: 'Darbhanga, Bihar',
      specialty: 'Mixed Media',
      bio: 'Neha Gupta experiments with mixed media, incorporating Mithila art elements into various materials and formats. Her work pushes the boundaries of traditional Mithila art while respecting its cultural roots.',
      profileImage: 'https://randomuser.me/api/portraits/women/56.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543214',
      featured: true,
      artworks: 31,
      joinedDate: new Date('2021-02-18')
    },
    {
      id: 6,
      name: 'Vikram Jha',
      location: 'Kolkata, West Bengal',
      specialty: 'Painting',
      bio: 'Vikram Jha is known for his large-scale Mithila paintings that often address contemporary social issues. His work has been recognized for its bold themes and meticulous execution.',
      profileImage: 'https://randomuser.me/api/portraits/men/36.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543215',
      featured: false,
      artworks: 24,
      joinedDate: new Date('2020-07-30')
    }
  ];

  constructor(private http: HttpClient) {}
  
  getArtists(): Observable<Artist[]> {
    // In a real app, this would call the API
    // return this.http.get<Artist[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockArtists).pipe(delay(800));
  }
  
  getArtistById(id: number): Observable<Artist> {
    // In a real app, this would call the API
    // return this.http.get<Artist>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const artist = this.mockArtists.find(a => a.id === id);
    return of(artist as Artist).pipe(delay(500));
  }
  
  addArtist(artist: Artist): Observable<Artist> {
    // In a real app, this would call the API
    // return this.http.post<Artist>(this.apiUrl, artist);
    
    // For demo, add to mock data with simulated delay
    const newArtist = {
      ...artist,
      id: this.getNextId(),
      artworks: 0,
      joinedDate: new Date()
    };
    this.mockArtists.push(newArtist);
    return of(newArtist).pipe(delay(800));
  }
  
  updateArtist(artist: Artist): Observable<Artist> {
    // In a real app, this would call the API
    // return this.http.put<Artist>(`${this.apiUrl}/${artist.id}`, artist);
    
    // For demo, update mock data with simulated delay
    const index = this.mockArtists.findIndex(a => a.id === artist.id);
    if (index !== -1) {
      this.mockArtists[index] = { ...artist };
    }
    return of(artist).pipe(delay(800));
  }
  
  deleteArtist(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockArtists.findIndex(a => a.id === id);
    if (index !== -1) {
      this.mockArtists.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
  
  private getNextId(): number {
    return Math.max(...this.mockArtists.map(a => a.id)) + 1;
  }
}
