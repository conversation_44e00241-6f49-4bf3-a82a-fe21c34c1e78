import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { Router } from '@angular/router';

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: string;
  token: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<AdminUser | null>;
  public currentUser: Observable<AdminUser | null>;

  // For demo purposes - in production this would come from environment
  private apiUrl = 'api/auth';

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.currentUserSubject = new BehaviorSubject<AdminUser | null>(
      this.getUserFromLocalStorage()
    );
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): AdminUser | null {
    return this.currentUserSubject.value;
  }

  login(email: string, password: string): Observable<AdminUser> {
    // In a real app, this would call the API
    // For demo, we'll simulate a successful login with mock data
    if (email === '<EMAIL>' && password === 'admin123') {
      const user: AdminUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        token: 'mock-jwt-token'
      };

      localStorage.setItem('currentUser', JSON.stringify(user));
      this.currentUserSubject.next(user);
      return of(user);
    }

    return throwError(() => new Error('Invalid email or password'));
  }

  logout(): void {
    localStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
    this.router.navigate(['/admin/login']);
  }

  isAuthenticated(): boolean {
    return !!this.currentUserValue;
  }

  private getUserFromLocalStorage(): AdminUser | null {
    const userJson = localStorage.getItem('currentUser');
    if (userJson) {
      try {
        return JSON.parse(userJson);
      } catch (e) {
        localStorage.removeItem('currentUser');
        return null;
      }
    }
    return null;
  }
}
