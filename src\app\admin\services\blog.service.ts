import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { BlogPost } from '../models/blog-post.model';

@Injectable({
  providedIn: 'root'
})
export class BlogService {
  private apiUrl = 'api/blog';
  
  // Mock data for demo purposes
  private mockBlogPosts: BlogPost[] = [
    {
      id: 1,
      title: 'The Rich Heritage of Mithila Art',
      author: '<PERSON><PERSON>',
      authorImageUrl: 'https://randomuser.me/api/portraits/women/12.jpg',
      content: 'Mithila art, also known as Madhubani painting, is a traditional art form practiced in the Mithila region of India and Nepal. This ancient art form is characterized by geometric patterns, mythological motifs, and vibrant colors. In this article, we explore the rich heritage and cultural significance of Mithila art...',
      category: 'Art',
      imageUrl: 'https://images.unsplash.com/photo-1582201942988-13e60e4556ee?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8bWFkaHViYW5pfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      publishDate: new Date('2023-01-15'),
      status: 'Published',
      tags: ['Mithila', 'Art', 'Culture', 'Heritage']
    },
    {
      id: 2,
      title: 'Meet the Master Artists of Mithila',
      author: 'Rajesh Kumar',
      authorImageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
      content: 'The Mithila region has produced many master artists who have dedicated their lives to preserving and evolving the traditional art form. In this feature, we introduce you to some of the most renowned Mithila artists, their unique styles, and their contributions to the art world...',
      category: 'Artists',
      imageUrl: 'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YXJ0aXN0fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      publishDate: new Date('2023-02-20'),
      status: 'Published',
      tags: ['Artists', 'Profiles', 'Mithila']
    },
    {
      id: 3,
      title: 'Upcoming Exhibition: Colors of Mithila',
      author: 'Anita Devi',
      authorImageUrl: 'https://randomuser.me/api/portraits/women/45.jpg',
      content: 'We are excited to announce our upcoming exhibition, "Colors of Mithila," featuring works from over 20 artists from the Mithila region. The exhibition will showcase both traditional and contemporary interpretations of Mithila art, offering visitors a comprehensive view of this vibrant art form...',
      category: 'Events',
      imageUrl: 'https://images.unsplash.com/photo-1531058020387-3be344556be6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8ZXhoaWJpdGlvbnxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      publishDate: new Date('2023-03-10'),
      status: 'Published',
      tags: ['Exhibition', 'Events', 'Announcement']
    },
    {
      id: 4,
      title: 'The Evolution of Mithila Art in Modern Times',
      author: 'Sunil Jha',
      authorImageUrl: 'https://randomuser.me/api/portraits/men/67.jpg',
      content: 'While Mithila art has ancient roots, it continues to evolve in response to contemporary influences. This article explores how modern Mithila artists are innovating within the tradition, incorporating new themes, techniques, and materials while maintaining the essence of this cultural heritage...',
      category: 'Art',
      imageUrl: 'https://images.unsplash.com/photo-1513364776144-60967b0f800f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8YXJ0JTIwZXZvbHV0aW9ufGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      publishDate: new Date('2023-04-05'),
      status: 'Draft',
      tags: ['Contemporary', 'Evolution', 'Modern Art']
    },
    {
      id: 5,
      title: 'Workshop Recap: Learning Mithila Painting Techniques',
      author: 'Meena Gupta',
      authorImageUrl: 'https://randomuser.me/api/portraits/women/22.jpg',
      content: 'Last weekend, we hosted a successful workshop on Mithila painting techniques, led by master artist Sunita Devi. Over 30 participants learned about traditional materials, color preparation, and basic motifs. Here\'s a recap of the event, including photos and participant feedback...',
      category: 'Events',
      imageUrl: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8d29ya3Nob3B8ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
      publishDate: new Date('2023-05-12'),
      status: 'Published',
      tags: ['Workshop', 'Learning', 'Techniques']
    }
  ];

  constructor(private http: HttpClient) {}
  
  getBlogPosts(): Observable<BlogPost[]> {
    // In a real app, this would call the API
    // return this.http.get<BlogPost[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockBlogPosts).pipe(delay(800));
  }
  
  getBlogPostById(id: number): Observable<BlogPost> {
    // In a real app, this would call the API
    // return this.http.get<BlogPost>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const post = this.mockBlogPosts.find(p => p.id === id);
    return of(post as BlogPost).pipe(delay(500));
  }
  
  addBlogPost(post: BlogPost): Observable<BlogPost> {
    // In a real app, this would call the API
    // return this.http.post<BlogPost>(this.apiUrl, post);
    
    // For demo, add to mock data with simulated delay
    const newPost = {
      ...post,
      id: this.getNextId(),
      publishDate: new Date(),
      tags: post.tags || []
    };
    this.mockBlogPosts.push(newPost);
    return of(newPost).pipe(delay(800));
  }
  
  updateBlogPost(post: BlogPost): Observable<BlogPost> {
    // In a real app, this would call the API
    // return this.http.put<BlogPost>(`${this.apiUrl}/${post.id}`, post);
    
    // For demo, update mock data with simulated delay
    const index = this.mockBlogPosts.findIndex(p => p.id === post.id);
    if (index !== -1) {
      this.mockBlogPosts[index] = { ...post };
    }
    return of(post).pipe(delay(800));
  }
  
  deleteBlogPost(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockBlogPosts.findIndex(p => p.id === id);
    if (index !== -1) {
      this.mockBlogPosts.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
  
  private getNextId(): number {
    return Math.max(...this.mockBlogPosts.map(p => p.id)) + 1;
  }
}
