import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { ContactMessage } from '../models/contact-message.model';

@Injectable({
  providedIn: 'root'
})
export class ContactService {
  private apiUrl = 'api/contact';
  
  // Mock data for demo purposes
  private mockMessages: ContactMessage[] = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      subject: 'Inquiry about Mithila Paintings',
      message: 'Hello, I am interested in purchasing some Mithila paintings for my new home. Could you please provide information about the available artworks, their prices, and shipping options to Delhi? Thank you.',
      submittedAt: new Date('2023-06-15T10:30:00'),
      isRead: true,
      status: 'Resolved',
      notes: '2023-06-15 14:45: Sent catalog of available Mithila paintings with prices and shipping details.'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 9876543211',
      subject: 'Workshop Registration',
      message: 'I would like to register for the upcoming Madhubani painting workshop scheduled for next month. Could you please share the registration details and fee structure? I am a beginner with no prior experience in painting.',
      submittedAt: new Date('2023-06-18T15:45:00'),
      isRead: true,
      status: 'In Progress',
      notes: '2023-06-18 17:20: Sent workshop registration form and fee details.\n2023-06-19 10:15: Followed up with additional information about materials needed for beginners.'
    },
    {
      id: 3,
      name: 'Amit Kumar',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      subject: 'Collaboration Proposal',
      message: 'I represent an art gallery in Mumbai and we are interested in collaborating with Mithilani Ghar for a joint exhibition featuring Mithila art. We would like to discuss the possibility of showcasing your artists\' work in our gallery. Please let me know if you are interested in this collaboration.',
      submittedAt: new Date('2023-06-20T09:15:00'),
      isRead: false,
      status: 'New',
      notes: ''
    },
    {
      id: 4,
      name: 'Neha Gupta',
      email: '<EMAIL>',
      phone: '+91 9876543213',
      subject: 'Custom Artwork Request',
      message: 'I am looking for a custom Mithila painting for my office. I would like a large piece (approximately 4x3 feet) depicting village life in Mithila region. Could you connect me with an artist who can create this custom piece? My budget is around ₹25,000-30,000.',
      submittedAt: new Date('2023-06-22T14:20:00'),
      isRead: false,
      status: 'New',
      notes: ''
    },
    {
      id: 5,
      name: 'Rajesh Jha',
      email: '<EMAIL>',
      phone: '+91 9876543214',
      subject: 'Website Feedback',
      message: 'I recently visited your website and found it very informative. However, I noticed that some of the images in the gallery section are not loading properly on mobile devices. Also, it would be helpful to have a filter option to sort artworks by price range. Just wanted to share this feedback to help improve the user experience.',
      submittedAt: new Date('2023-06-25T11:30:00'),
      isRead: true,
      status: 'Resolved',
      notes: '2023-06-25 13:45: Thanked for feedback and informed that the technical team will look into the image loading issue on mobile devices.\n2023-06-26 16:30: Notified that the issue has been fixed and price filter will be added in the next website update.'
    }
  ];

  constructor(private http: HttpClient) {}
  
  getContactMessages(): Observable<ContactMessage[]> {
    // In a real app, this would call the API
    // return this.http.get<ContactMessage[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockMessages).pipe(delay(800));
  }
  
  getContactMessageById(id: number): Observable<ContactMessage> {
    // In a real app, this would call the API
    // return this.http.get<ContactMessage>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const message = this.mockMessages.find(m => m.id === id);
    return of(message as ContactMessage).pipe(delay(500));
  }
  
  updateContactMessage(message: ContactMessage): Observable<ContactMessage> {
    // In a real app, this would call the API
    // return this.http.put<ContactMessage>(`${this.apiUrl}/${message.id}`, message);
    
    // For demo, update mock data with simulated delay
    const index = this.mockMessages.findIndex(m => m.id === message.id);
    if (index !== -1) {
      this.mockMessages[index] = { ...message };
    }
    return of(message).pipe(delay(800));
  }
  
  deleteContactMessage(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockMessages.findIndex(m => m.id === id);
    if (index !== -1) {
      this.mockMessages.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
}
