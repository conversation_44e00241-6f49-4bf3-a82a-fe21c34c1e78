import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Event } from '../models/event.model';

@Injectable({
  providedIn: 'root'
})
export class EventsService {
  private apiUrl = 'api/events';
  
  // Mock data for demo purposes
  private mockEvents: Event[] = [
    {
      id: 1,
      title: 'Mithila Art Exhibition',
      description: 'A showcase of traditional and contemporary Mithila art featuring works from renowned artists.',
      startDate: new Date('2023-08-15T10:00:00'),
      endDate: new Date('2023-08-20T18:00:00'),
      location: 'Mithilani Ghar Art Gallery, Patna',
      imageUrl: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YXJ0JTIwZXhoaWJpdGlvbnxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      organizer: 'Mithilani Ghar Cultural Society',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 9876543210',
      registrationUrl: 'https://mithilanighar.com/events/mithila-art-exhibition',
      maxAttendees: 200,
      currentAttendees: 145,
      status: 'Upcoming',
      isFeatured: true,
      createdAt: new Date('2023-06-10')
    },
    {
      id: 2,
      title: 'Madhubani Painting Workshop',
      description: 'Learn the traditional art of Madhubani painting from master artist Sunita Devi. All materials provided.',
      startDate: new Date('2023-07-25T14:00:00'),
      endDate: new Date('2023-07-25T17:00:00'),
      location: 'Mithilani Ghar Workshop Space, Patna',
      imageUrl: 'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YXJ0JTIwd29ya3Nob3B8ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
      organizer: 'Sunita Devi',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 9876543211',
      registrationUrl: 'https://mithilanighar.com/events/madhubani-workshop',
      maxAttendees: 30,
      currentAttendees: 28,
      status: 'Upcoming',
      isFeatured: true,
      createdAt: new Date('2023-06-15')
    },
    {
      id: 3,
      title: 'Mithila Cultural Festival',
      description: 'A celebration of Mithila culture featuring art, music, dance, and traditional food.',
      startDate: new Date('2023-09-05T10:00:00'),
      endDate: new Date('2023-09-07T20:00:00'),
      location: 'City Park, Darbhanga',
      imageUrl: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8ZmVzdGl2YWx8ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
      organizer: 'Mithila Cultural Association',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 9876543212',
      registrationUrl: 'https://mithilanighar.com/events/cultural-festival',
      maxAttendees: 500,
      currentAttendees: 320,
      status: 'Upcoming',
      isFeatured: false,
      createdAt: new Date('2023-06-20')
    },
    {
      id: 4,
      title: 'Artist Talk: Evolution of Mithila Art',
      description: 'Join us for an insightful talk by art historian Dr. Rajesh Kumar on the evolution of Mithila art through centuries.',
      startDate: new Date('2023-07-10T18:00:00'),
      endDate: new Date('2023-07-10T20:00:00'),
      location: 'Mithilani Ghar Auditorium, Patna',
      imageUrl: 'https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YXJ0JTIwdGFsa3xlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      organizer: 'Dr. Rajesh Kumar',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 9876543213',
      registrationUrl: 'https://mithilanighar.com/events/artist-talk',
      maxAttendees: 100,
      currentAttendees: 65,
      status: 'Completed',
      isFeatured: false,
      createdAt: new Date('2023-06-25')
    },
    {
      id: 5,
      title: 'Mithila Art Auction',
      description: 'Annual charity auction featuring rare and valuable Mithila artworks. All proceeds go to supporting local artists.',
      startDate: new Date('2023-10-15T17:00:00'),
      endDate: new Date('2023-10-15T21:00:00'),
      location: 'Grand Hotel, Patna',
      imageUrl: 'https://images.unsplash.com/photo-1551300704-5c333b4aa56b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8YXVjdGlvbnxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      organizer: 'Mithilani Ghar Foundation',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 9876543214',
      registrationUrl: 'https://mithilanighar.com/events/art-auction',
      maxAttendees: 150,
      currentAttendees: 0,
      status: 'Upcoming',
      isFeatured: true,
      createdAt: new Date('2023-07-01')
    }
  ];

  constructor(private http: HttpClient) {}
  
  getEvents(): Observable<Event[]> {
    // In a real app, this would call the API
    // return this.http.get<Event[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockEvents).pipe(delay(800));
  }
  
  getEventById(id: number): Observable<Event> {
    // In a real app, this would call the API
    // return this.http.get<Event>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const event = this.mockEvents.find(e => e.id === id);
    return of(event as Event).pipe(delay(500));
  }
  
  addEvent(event: Event): Observable<Event> {
    // In a real app, this would call the API
    // return this.http.post<Event>(this.apiUrl, event);
    
    // For demo, add to mock data with simulated delay
    const newEvent = {
      ...event,
      id: this.getNextId(),
      createdAt: new Date()
    };
    this.mockEvents.push(newEvent);
    return of(newEvent).pipe(delay(800));
  }
  
  updateEvent(event: Event): Observable<Event> {
    // In a real app, this would call the API
    // return this.http.put<Event>(`${this.apiUrl}/${event.id}`, event);
    
    // For demo, update mock data with simulated delay
    const index = this.mockEvents.findIndex(e => e.id === event.id);
    if (index !== -1) {
      this.mockEvents[index] = { ...event };
    }
    return of(event).pipe(delay(800));
  }
  
  deleteEvent(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockEvents.findIndex(e => e.id === id);
    if (index !== -1) {
      this.mockEvents.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
  
  private getNextId(): number {
    return Math.max(...this.mockEvents.map(e => e.id)) + 1;
  }
}
