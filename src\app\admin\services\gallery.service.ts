import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Artwork } from '../models/artwork.model';

@Injectable({
  providedIn: 'root'
})
export class GalleryService {
  private apiUrl = 'api/artworks';
  
  // Mock data for demo purposes
  private mockArtworks: Artwork[] = [
    {
      id: 1,
      title: 'Mithila Harmony',
      artist: '<PERSON><PERSON>',
      description: 'A beautiful Mithila painting depicting village life.',
      category: 'Painting',
      imageUrl: 'https://images.unsplash.com/photo-1579783902614-a3fb3927b6a5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8bWl0aGlsYSUyMHBhaW50aW5nfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      price: 12000,
      createdAt: new Date('2023-01-15')
    },
    {
      id: 2,
      title: '<PERSON><PERSON><PERSON><PERSON> Fish',
      artist: '<PERSON><PERSON>',
      description: 'Traditional Madhubani art featuring colorful fish motifs.',
      category: 'Painting',
      imageUrl: 'https://images.unsplash.com/photo-1582201942988-13e60e4556ee?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8bWFkaHViYW5pfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      price: 8500,
      createdAt: new Date('2023-02-20')
    },
    {
      id: 3,
      title: 'Elephant Procession',
      artist: 'Manoj Jha',
      description: 'A detailed sculpture showing a traditional elephant procession.',
      category: 'Sculpture',
      imageUrl: 'https://images.unsplash.com/photo-1569172131007-4a5089808c82?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Nnx8aW5kaWFuJTIwc2N1bHB0dXJlfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      price: 18000,
      createdAt: new Date('2023-03-10')
    },
    {
      id: 4,
      title: 'Digital Mithila',
      artist: 'Priya Singh',
      description: 'A modern digital interpretation of traditional Mithila art.',
      category: 'Digital Art',
      imageUrl: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZGlnaXRhbCUyMGFydHxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      price: 5000,
      createdAt: new Date('2023-04-05')
    },
    {
      id: 5,
      title: 'Village Life',
      artist: 'Amit Sharma',
      description: 'A photograph capturing the essence of rural life in Mithila region.',
      category: 'Photography',
      imageUrl: 'https://images.unsplash.com/photo-1531171074112-291d5807273d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8aW5kaWFuJTIwdmlsbGFnZXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      price: 3500,
      createdAt: new Date('2023-05-12')
    },
    {
      id: 6,
      title: 'Mithila Fusion',
      artist: 'Neha Gupta',
      description: 'A mixed media artwork combining traditional Mithila patterns with modern elements.',
      category: 'Mixed Media',
      imageUrl: 'https://images.unsplash.com/photo-1578926375605-eaf7559b1458?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8bWl4ZWQlMjBtZWRpYSUyMGFydHxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      price: 9500,
      createdAt: new Date('2023-06-18')
    }
  ];

  constructor(private http: HttpClient) {}
  
  getArtworks(): Observable<Artwork[]> {
    // In a real app, this would call the API
    // return this.http.get<Artwork[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockArtworks).pipe(delay(800));
  }
  
  getArtworkById(id: number): Observable<Artwork> {
    // In a real app, this would call the API
    // return this.http.get<Artwork>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const artwork = this.mockArtworks.find(a => a.id === id);
    return of(artwork as Artwork).pipe(delay(500));
  }
  
  addArtwork(artwork: Artwork): Observable<Artwork> {
    // In a real app, this would call the API
    // return this.http.post<Artwork>(this.apiUrl, artwork);
    
    // For demo, add to mock data with simulated delay
    const newArtwork = {
      ...artwork,
      id: this.getNextId(),
      createdAt: new Date()
    };
    this.mockArtworks.push(newArtwork);
    return of(newArtwork).pipe(delay(800));
  }
  
  updateArtwork(artwork: Artwork): Observable<Artwork> {
    // In a real app, this would call the API
    // return this.http.put<Artwork>(`${this.apiUrl}/${artwork.id}`, artwork);
    
    // For demo, update mock data with simulated delay
    const index = this.mockArtworks.findIndex(a => a.id === artwork.id);
    if (index !== -1) {
      this.mockArtworks[index] = { ...artwork };
    }
    return of(artwork).pipe(delay(800));
  }
  
  deleteArtwork(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockArtworks.findIndex(a => a.id === id);
    if (index !== -1) {
      this.mockArtworks.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
  
  private getNextId(): number {
    return Math.max(...this.mockArtworks.map(a => a.id)) + 1;
  }
}
