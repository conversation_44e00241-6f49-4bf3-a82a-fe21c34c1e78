import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Product } from '../models/product.model';

@Injectable({
  providedIn: 'root'
})
export class ShopService {
  private apiUrl = 'api/products';
  
  // Mock data for demo purposes
  private mockProducts: Product[] = [
    {
      id: 1,
      name: 'Madhubani Wall Art',
      sku: 'MWA001',
      description: 'Beautiful handmade Madhubani painting on handmade paper.',
      category: 'Paintings',
      price: 3500,
      salePrice: 2999,
      stockQuantity: 15,
      imageUrl: 'https://images.unsplash.com/photo-1582201942988-13e60e4556ee?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8bWFkaHViYW5pfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      status: 'In Stock',
      createdAt: new Date('2023-01-10')
    },
    {
      id: 2,
      name: 'Handcrafted Wooden Elephant',
      sku: 'HWE002',
      description: 'Intricately carved wooden elephant with traditional Mithila patterns.',
      category: 'Handicrafts',
      price: 1800,
      salePrice: null,
      stockQuantity: 8,
      imageUrl: 'https://images.unsplash.com/photo-1569172131007-4a5089808c82?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Nnx8aW5kaWFuJTIwc2N1bHB0dXJlfGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=500&q=60',
      status: 'In Stock',
      createdAt: new Date('2023-02-15')
    },
    {
      id: 3,
      name: 'Mithila Print Cotton Saree',
      sku: 'MPS003',
      description: 'Handwoven cotton saree with traditional Mithila prints.',
      category: 'Clothing',
      price: 4500,
      salePrice: null,
      stockQuantity: 3,
      imageUrl: 'https://images.unsplash.com/photo-1610030469668-8e4a7c2f1e1f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8c2FyZWV8ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
      status: 'Low Stock',
      createdAt: new Date('2023-03-05')
    },
    {
      id: 4,
      name: 'Handmade Silver Earrings',
      sku: 'HSE004',
      description: 'Silver earrings with traditional Mithila motifs.',
      category: 'Jewelry',
      price: 2200,
      salePrice: 1899,
      stockQuantity: 12,
      imageUrl: 'https://images.unsplash.com/photo-1535632787350-4e68ef0ac584?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NXx8ZWFycmluZ3N8ZW58MHx8MHx8&auto=format&fit=crop&w=500&q=60',
      status: 'In Stock',
      createdAt: new Date('2023-03-20')
    },
    {
      id: 5,
      name: 'Mithila Cushion Covers (Set of 2)',
      sku: 'MCC005',
      description: 'Cotton cushion covers with hand-painted Mithila designs.',
      category: 'Home Decor',
      price: 1200,
      salePrice: null,
      stockQuantity: 0,
      imageUrl: 'https://images.unsplash.com/photo-1540638349517-3abd5afc5847?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8Y3VzaGlvbnxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      status: 'Out of Stock',
      createdAt: new Date('2023-04-10')
    },
    {
      id: 6,
      name: 'Handmade Paper Journal',
      sku: 'HPJ006',
      description: 'Journal with handmade paper and Mithila art cover.',
      category: 'Handicrafts',
      price: 850,
      salePrice: 699,
      stockQuantity: 25,
      imageUrl: 'https://images.unsplash.com/photo-1531346680769-a1d79b57de5c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8am91cm5hbHxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=500&q=60',
      status: 'In Stock',
      createdAt: new Date('2023-05-05')
    }
  ];

  constructor(private http: HttpClient) {}
  
  getProducts(): Observable<Product[]> {
    // In a real app, this would call the API
    // return this.http.get<Product[]>(this.apiUrl);
    
    // For demo, return mock data with simulated delay
    return of(this.mockProducts).pipe(delay(800));
  }
  
  getProductById(id: number): Observable<Product> {
    // In a real app, this would call the API
    // return this.http.get<Product>(`${this.apiUrl}/${id}`);
    
    // For demo, find in mock data with simulated delay
    const product = this.mockProducts.find(p => p.id === id);
    return of(product as Product).pipe(delay(500));
  }
  
  addProduct(product: Product): Observable<Product> {
    // In a real app, this would call the API
    // return this.http.post<Product>(this.apiUrl, product);
    
    // For demo, add to mock data with simulated delay
    const newProduct = {
      ...product,
      id: this.getNextId(),
      createdAt: new Date()
    };
    this.mockProducts.push(newProduct);
    return of(newProduct).pipe(delay(800));
  }
  
  updateProduct(product: Product): Observable<Product> {
    // In a real app, this would call the API
    // return this.http.put<Product>(`${this.apiUrl}/${product.id}`, product);
    
    // For demo, update mock data with simulated delay
    const index = this.mockProducts.findIndex(p => p.id === product.id);
    if (index !== -1) {
      this.mockProducts[index] = { ...product };
    }
    return of(product).pipe(delay(800));
  }
  
  deleteProduct(id: number): Observable<void> {
    // In a real app, this would call the API
    // return this.http.delete<void>(`${this.apiUrl}/${id}`);
    
    // For demo, remove from mock data with simulated delay
    const index = this.mockProducts.findIndex(p => p.id === id);
    if (index !== -1) {
      this.mockProducts.splice(index, 1);
    }
    return of(undefined).pipe(delay(800));
  }
  
  private getNextId(): number {
    return Math.max(...this.mockProducts.map(p => p.id)) + 1;
  }
}
