import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Shop, ShopServiceProxy } from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './shop.component.html',
  styleUrls: ['./shop.component.css']
})
export class ShopComponent implements OnInit {
  Shops: Shop[] = [];
  selectedShop: Shop | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'

  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Paintings', 'Handicrafts', 'Clothing', 'Jewelry', 'Home Decor'];

  constructor(private shopService: ShopServiceProxy) {}

  ngOnInit(): void {
    this.loadShops();
  }

  loadShops(): void {
    this.isLoading = true;
    this.shopService.getAllShop().subscribe({
      next: (Shops: Shop[]) => {
        this.Shops = Shops;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading Shops', error);
        this.isLoading = false;
      }
    });
  }

  openAddModal(): void {
    this.selectedShop = new Shop();
    this.modalType = 'add';
    this.showModal = true;
  }

  openEditModal(Shop: Shop): void {
    this.selectedShop = { ...Shop };
    this.modalType = 'edit';
    this.showModal = true;
  }

  openDeleteModal(Shop: Shop): void {
    this.selectedShop = Shop;
    this.modalType = 'delete';
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedShop = null;
  }

  saveShop(): void {
    if (!this.selectedShop) return;

    // Validate required fields
    if (!this.selectedShop.name || !this.selectedShop.category || this.selectedShop.price === null) {
      alert('Please fill in all required fields (Name, Category, Price)');
      return;
    }

    // Ensure all fields are properly set (not undefined)
    if (!this.selectedShop.name.trim() || !this.selectedShop.category.trim()) {
      alert('Please fill in all required fields with valid values');
      return;
    }

    console.log('Saving Shop:', this.selectedShop);

    if (this.modalType === 'add') {
      this.shopService.createShop(this.selectedShop).subscribe({
        next: (res: any) => {
          console.log('Shop added successfully:', res);
          this.loadShops();
          this.closeModal();
        },
        error: (error: any) => {
          console.error('Error adding Shop:', error);
          this.handleSaveError(error);
        }
      });
    } else if (this.modalType === 'edit') {
      this.shopService.createShop(this.selectedShop).subscribe({
        next: (res: any) => {
          console.log('Shop updated successfully:', res);
          this.loadShops();
          this.closeModal();
        },
        error: (error: any) => {
          console.error('Error updating Shop:', error);
          this.handleSaveError(error);
        }
      });
    }
  }

  private handleSaveError(error: any): void {
    // Handle validation errors
    if (error.status === 400 && error.error && error.error.errors) {
      console.log('Full error response:', error);
      let errorMessage = 'Validation errors:\n';
      for (const field in error.error.errors) {
        errorMessage += `${field}: ${error.error.errors[field].join(', ')}\n`;
      }
      alert(errorMessage);
    } else {
      console.log('Full error response:', error);
      alert('Failed to save Shop. Please try again.');
    }
  }

  deleteShop(): void {
    if (!this.selectedShop) return;

    this.shopService.deleteShop(this.selectedShop.id).subscribe({
      next: () => {
        this.loadShops();
        this.closeModal();
      },
      error: (error: any) => console.error('Error deleting Shop', error)
    });
  }

  get filteredShops(): Shop[] {
    return this.Shops.filter(Shop => {
      const matchesSearch = this.searchTerm === '' ||
        (Shop.name && Shop.name.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
        (Shop.description && Shop.description.toLowerCase().includes(this.searchTerm.toLowerCase()));

      const matchesCategory = this.categoryFilter === '' ||
        Shop.category === this.categoryFilter;

      return matchesSearch && matchesCategory;
    });
  }
}
