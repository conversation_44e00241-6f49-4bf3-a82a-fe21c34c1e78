import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ShopService } from '../services/shop.service';
import { Product } from '../models/product.model';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './shop.component.html',
  styleUrls: ['./shop.component.css']
})
export class ShopComponent implements OnInit {
  products: Product[] = [];
  selectedProduct: Product | null = null;
  isLoading = true;
  showModal = false;
  modalType = ''; // 'add', 'edit', 'delete'
  
  // Filters
  searchTerm = '';
  categoryFilter = '';
  categories = ['Paintings', 'Handicrafts', 'Clothing', 'Jewelry', 'Home Decor'];
  
  constructor(private shopService: ShopService) {}

  ngOnInit(): void {
    this.loadProducts();
  }
  
  loadProducts(): void {
    this.isLoading = true;
    this.shopService.getProducts().subscribe({
      next: (products) => {
        this.products = products;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading products', error);
        this.isLoading = false;
      }
    });
  }
  
  openAddModal(): void {
    this.selectedProduct = new Product();
    this.modalType = 'add';
    this.showModal = true;
  }
  
  openEditModal(product: Product): void {
    this.selectedProduct = { ...product };
    this.modalType = 'edit';
    this.showModal = true;
  }
  
  openDeleteModal(product: Product): void {
    this.selectedProduct = product;
    this.modalType = 'delete';
    this.showModal = true;
  }
  
  closeModal(): void {
    this.showModal = false;
    this.selectedProduct = null;
  }
  
  saveProduct(): void {
    if (!this.selectedProduct) return;
    
    if (this.modalType === 'add') {
      this.shopService.addProduct(this.selectedProduct).subscribe({
        next: () => {
          this.loadProducts();
          this.closeModal();
        },
        error: (error) => console.error('Error adding product', error)
      });
    } else if (this.modalType === 'edit') {
      this.shopService.updateProduct(this.selectedProduct).subscribe({
        next: () => {
          this.loadProducts();
          this.closeModal();
        },
        error: (error) => console.error('Error updating product', error)
      });
    }
  }
  
  deleteProduct(): void {
    if (!this.selectedProduct) return;
    
    this.shopService.deleteProduct(this.selectedProduct.id).subscribe({
      next: () => {
        this.loadProducts();
        this.closeModal();
      },
      error: (error) => console.error('Error deleting product', error)
    });
  }
  
  get filteredProducts(): Product[] {
    return this.products.filter(product => {
      const matchesSearch = this.searchTerm === '' || 
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(this.searchTerm.toLowerCase());
        
      const matchesCategory = this.categoryFilter === '' || 
        product.category === this.categoryFilter;
        
      return matchesSearch && matchesCategory;
    });
  }
}
