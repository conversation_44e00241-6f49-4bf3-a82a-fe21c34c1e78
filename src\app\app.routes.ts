import { Routes } from '@angular/router';
import { MainLayoutComponent } from './components/layout/main-layout/main-layout.component';

export const routes: Routes = [
  { path: 'admin', loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule), data: { preload: true } },
  { path: 'auth', loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule), data: { preload: true } },
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
        title: '<PERSON><PERSON><PERSON> - Home'
      },
      {
        path: 'about',
        loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent),
        title: 'About Us - Mi<PERSON><PERSON>'
      },
      {
        path: 'gallery',
        loadComponent: () => import('./pages/gallery/gallery.component').then(m => m.GalleryComponent),
        title: 'Gallery - <PERSON><PERSON><PERSON>'
      },
      {
        path: 'shop',
        loadComponent: () => import('./pages/shop/shop.component').then(m => m.ShopComponent),
        title: 'Shop - Mithilani Ghar'
      },
      {
        path: 'shop/:id',
        loadComponent: () => import('./pages/product-detail/product-detail.component').then(m => m.ProductDetailComponent),
        title: 'Product Details - Mithilani Ghar'
      },
      {
        path: 'artists',
        loadComponent: () => import('./pages/artists/artists.component').then(m => m.ArtistsComponent),
        title: 'Artists - Mithilani Ghar'
      },
      {
        path: 'artists/:id',
        loadComponent: () => import('./pages/artist-detail/artist-detail.component').then(m => m.ArtistDetailComponent),
        title: 'Artist Details - Mithilani Ghar'
      },
      {
        path: 'events',
        loadComponent: () => import('./pages/events/events.component').then(m => m.EventsComponent),
        title: 'Events - Mithilani Ghar'
      },
      {
        path: 'blog',
        loadComponent: () => import('./pages/blog/blog.component').then(m => m.BlogComponent),
        title: 'Blog - Mithilani Ghar'
      },
      {
        path: 'blog/:id',
        loadComponent: () => import('./pages/blog-post/blog-post.component').then(m => m.BlogPostComponent),
        title: 'Blog Post - Mithilani Ghar'
      },
      {
        path: 'contact',
        loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent),
        title: 'Contact Us - Mithilani Ghar'
      }
    ]
  },
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full'
  }
];
