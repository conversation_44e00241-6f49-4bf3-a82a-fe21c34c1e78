<div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:shadow-xl">
  <div class="p-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-display font-bold text-gray-800">Forgot Password</h1>
      <p class="text-gray-600 mt-2">Enter your email to reset your password</p>
    </div>
    
    <!-- Alert for errors -->
    <div *ngIf="error" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
      <p class="font-medium">Error</p>
      <p>{{ error }}</p>
    </div>
    
    <!-- Success message -->
    <div *ngIf="success" class="mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded" role="alert">
      <p class="font-medium">Success</p>
      <p>{{ success }}</p>
    </div>
    
    <!-- Forgot Password Form -->
    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="space-y-6">
      <!-- Email Field -->
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">email</span>
          </div>
          <input 
            id="email" 
            type="email" 
            formControlName="email" 
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['email'].errors }"
            placeholder="<EMAIL>"
          >
        </div>
        <div *ngIf="submitted && f['email'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['email'].errors['required']">Email is required</div>
          <div *ngIf="f['email'].errors['email']">Please enter a valid email address</div>
        </div>
      </div>
      
      <!-- Submit Button -->
      <div>
        <button 
          type="submit" 
          [disabled]="loading"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mithila-red hover:bg-mithila-red-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mithila-red transition-colors"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Reset Password
        </button>
      </div>
    </form>
    
    <!-- Back to Login Link -->
    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Remember your password?
        <a routerLink="/auth/login" class="font-medium text-mithila-blue hover:text-mithila-red transition-colors">
          Back to login
        </a>
      </p>
    </div>
  </div>
</div>
