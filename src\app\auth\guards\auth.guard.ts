import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}
  
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.authService.isAuthenticated()) {
      // Check if route has data.roles and user has one of required roles
      if (route.data['roles'] && !this.checkRoles(route.data['roles'])) {
        // User doesn't have required role, redirect to home page
        this.router.navigate(['/']);
        return false;
      }
      
      return true;
    }
    
    // Not logged in, redirect to login page
    this.router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }
  
  private checkRoles(roles: string[]): boolean {
    const userRole = this.authService.currentUserValue?.role;
    if (!userRole) return false;
    
    return roles.includes(userRole);
  }
}
