import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-auth-layout',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './auth-layout.component.html',
  styleUrls: ['./auth-layout.component.css']
})
export class AuthLayoutComponent {
  // Background images for the auth layout
  backgroundImages = [
    'assets/images/auth/mithila-art-1.jpg',
    'assets/images/auth/mithila-art-2.jpg',
    'assets/images/auth/mithila-art-3.jpg'
  ];
  
  // Randomly select a background image
  selectedBackground = this.backgroundImages[Math.floor(Math.random() * this.backgroundImages.length)];
}
