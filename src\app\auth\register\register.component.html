<div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:shadow-xl">
  <div class="p-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-display font-bold text-gray-800">Create an Account</h1>
      <p class="text-gray-600 mt-2">Join the Mithilani Ghar community</p>
    </div>
    
    <!-- Alert for errors -->
    <div *ngIf="error" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
      <p class="font-medium">Error</p>
      <p>{{ error }}</p>
    </div>
    
    <!-- Registration Form -->
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="space-y-5">
      <!-- Name Fields (2 columns) -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- First Name -->
        <div>
          <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
          <input 
            id="firstName" 
            type="text" 
            formControlName="firstName" 
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['firstName'].errors }"
            placeholder="First Name"
          >
          <div *ngIf="submitted && f['firstName'].errors" class="mt-1 text-red-500 text-xs">
            <div *ngIf="f['firstName'].errors['required']">First name is required</div>
          </div>
        </div>
        
        <!-- Last Name -->
        <div>
          <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
          <input 
            id="lastName" 
            type="text" 
            formControlName="lastName" 
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['lastName'].errors }"
            placeholder="Last Name"
          >
          <div *ngIf="submitted && f['lastName'].errors" class="mt-1 text-red-500 text-xs">
            <div *ngIf="f['lastName'].errors['required']">Last name is required</div>
          </div>
        </div>
      </div>
      
      <!-- Username Field -->
      <div>
        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">person</span>
          </div>
          <input 
            id="username" 
            type="text" 
            formControlName="username" 
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['username'].errors }"
            placeholder="Choose a username"
          >
        </div>
        <div *ngIf="submitted && f['username'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['username'].errors['required']">Username is required</div>
          <div *ngIf="f['username'].errors['minlength']">Username must be at least 4 characters</div>
        </div>
      </div>
      
      <!-- Email Field -->
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">email</span>
          </div>
          <input 
            id="email" 
            type="email" 
            formControlName="email" 
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['email'].errors }"
            placeholder="<EMAIL>"
          >
        </div>
        <div *ngIf="submitted && f['email'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['email'].errors['required']">Email is required</div>
          <div *ngIf="f['email'].errors['email']">Please enter a valid email address</div>
        </div>
      </div>
      
      <!-- Phone Number Field (Optional) -->
      <div>
        <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-1">
          Phone Number <span class="text-gray-400">(Optional)</span>
        </label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">phone</span>
          </div>
          <input 
            id="phoneNumber" 
            type="tel" 
            formControlName="phoneNumber" 
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['phoneNumber'].errors }"
            placeholder="+91 9876543210"
          >
        </div>
        <div *ngIf="submitted && f['phoneNumber'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['phoneNumber'].errors['pattern']">Please enter a valid phone number</div>
        </div>
      </div>
      
      <!-- Password Field -->
      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">lock</span>
          </div>
          <input 
            id="password" 
            [type]="showPassword ? 'text' : 'password'" 
            formControlName="password" 
            class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['password'].errors }"
            placeholder="••••••••"
          >
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button 
              type="button" 
              (click)="togglePasswordVisibility()" 
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <span class="material-icons text-lg">{{ showPassword ? 'visibility_off' : 'visibility' }}</span>
            </button>
          </div>
        </div>
        <div *ngIf="submitted && f['password'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['password'].errors['required']">Password is required</div>
          <div *ngIf="f['password'].errors['minlength']">Password must be at least 8 characters</div>
        </div>
      </div>
      
      <!-- Confirm Password Field -->
      <div>
        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">lock</span>
          </div>
          <input 
            id="confirmPassword" 
            [type]="showConfirmPassword ? 'text' : 'password'" 
            formControlName="confirmPassword" 
            class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['confirmPassword'].errors }"
            placeholder="••••••••"
          >
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button 
              type="button" 
              (click)="toggleConfirmPasswordVisibility()" 
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <span class="material-icons text-lg">{{ showConfirmPassword ? 'visibility_off' : 'visibility' }}</span>
            </button>
          </div>
        </div>
        <div *ngIf="submitted && f['confirmPassword'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['confirmPassword'].errors['required']">Please confirm your password</div>
          <div *ngIf="f['confirmPassword'].errors['passwordMismatch']">Passwords do not match</div>
        </div>
      </div>
      
      <!-- Terms and Conditions Checkbox -->
      <div class="flex items-start">
        <div class="flex items-center h-5">
          <input 
            id="agreeTerms" 
            type="checkbox" 
            formControlName="agreeTerms"
            class="h-4 w-4 text-mithila-blue focus:ring-mithila-blue border-gray-300 rounded"
            [ngClass]="{ 'border-red-500': submitted && f['agreeTerms'].errors }"
          >
        </div>
        <div class="ml-3 text-sm">
          <label for="agreeTerms" class="font-medium text-gray-700">
            I agree to the 
            <a href="/terms" class="text-mithila-blue hover:text-mithila-red">Terms of Service</a> 
            and 
            <a href="/privacy" class="text-mithila-blue hover:text-mithila-red">Privacy Policy</a>
          </label>
          <div *ngIf="submitted && f['agreeTerms'].errors" class="mt-1 text-red-500 text-xs">
            <div *ngIf="f['agreeTerms'].errors['required']">You must agree to the terms and conditions</div>
          </div>
        </div>
      </div>
      
      <!-- Submit Button -->
      <div>
        <button 
          type="submit" 
          [disabled]="loading"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mithila-red hover:bg-mithila-red-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mithila-red transition-colors"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Create Account
        </button>
      </div>
    </form>
    
    <!-- Login Link -->
    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Already have an account?
        <a routerLink="/auth/login" class="font-medium text-mithila-blue hover:text-mithila-red transition-colors">
          Sign in
        </a>
      </p>
    </div>
  </div>
</div>
