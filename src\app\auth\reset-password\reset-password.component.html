<div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:shadow-xl">
  <div class="p-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-2xl font-display font-bold text-gray-800">Reset Password</h1>
      <p class="text-gray-600 mt-2">Create a new password for your account</p>
    </div>
    
    <!-- Alert for errors -->
    <div *ngIf="error" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
      <p class="font-medium">Error</p>
      <p>{{ error }}</p>
    </div>
    
    <!-- Success message -->
    <div *ngIf="success" class="mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded" role="alert">
      <p class="font-medium">Success</p>
      <p>{{ success }}</p>
    </div>
    
    <!-- Reset Password Form -->
    <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="space-y-6">
      <!-- Password Field -->
      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">lock</span>
          </div>
          <input 
            id="password" 
            [type]="showPassword ? 'text' : 'password'" 
            formControlName="password" 
            class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['password'].errors }"
            placeholder="••••••••"
          >
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button 
              type="button" 
              (click)="togglePasswordVisibility()" 
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <span class="material-icons text-lg">{{ showPassword ? 'visibility_off' : 'visibility' }}</span>
            </button>
          </div>
        </div>
        <div *ngIf="submitted && f['password'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['password'].errors['required']">Password is required</div>
          <div *ngIf="f['password'].errors['minlength']">Password must be at least 8 characters</div>
        </div>
      </div>
      
      <!-- Confirm Password Field -->
      <div>
        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-lg">lock</span>
          </div>
          <input 
            id="confirmPassword" 
            [type]="showConfirmPassword ? 'text' : 'password'" 
            formControlName="confirmPassword" 
            class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-mithila-blue focus:border-mithila-blue sm:text-sm"
            [ngClass]="{ 'border-red-500': submitted && f['confirmPassword'].errors }"
            placeholder="••••••••"
          >
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button 
              type="button" 
              (click)="toggleConfirmPasswordVisibility()" 
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <span class="material-icons text-lg">{{ showConfirmPassword ? 'visibility_off' : 'visibility' }}</span>
            </button>
          </div>
        </div>
        <div *ngIf="submitted && f['confirmPassword'].errors" class="mt-1 text-red-500 text-xs">
          <div *ngIf="f['confirmPassword'].errors['required']">Please confirm your password</div>
          <div *ngIf="f['confirmPassword'].errors['passwordMismatch']">Passwords do not match</div>
        </div>
      </div>
      
      <!-- Password Requirements -->
      <div class="bg-gray-50 p-3 rounded-md text-xs text-gray-600">
        <p class="font-medium text-gray-700 mb-1">Password requirements:</p>
        <ul class="list-disc pl-5 space-y-1">
          <li>At least 8 characters long</li>
          <li>Include at least one uppercase letter</li>
          <li>Include at least one number</li>
          <li>Include at least one special character</li>
        </ul>
      </div>
      
      <!-- Submit Button -->
      <div>
        <button 
          type="submit" 
          [disabled]="loading"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-mithila-red hover:bg-mithila-red-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-mithila-red transition-colors"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Reset Password
        </button>
      </div>
    </form>
    
    <!-- Back to Login Link -->
    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        <a routerLink="/auth/login" class="font-medium text-mithila-blue hover:text-mithila-red transition-colors">
          Back to login
        </a>
      </p>
    </div>
  </div>
</div>
