import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { Router } from '@angular/router';

export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  token: string;
  refreshToken?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  email: string;
  password: string;
  confirmPassword: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;

  // For demo purposes - in production this would come from environment
  private apiUrl = 'api/auth';

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.currentUserSubject = new BehaviorSubject<User | null>(
      this.getUserFromLocalStorage()
    );
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  login(loginRequest: LoginRequest): Observable<User> {
    // In a real app, this would call the API
    // return this.http.post<User>(`${this.apiUrl}/login`, loginRequest)
    //   .pipe(
    //     tap(user => {
    //       this.storeUserData(user, loginRequest.rememberMe);
    //     })
    //   );

    // For demo, we'll simulate a successful login with mock data
    if (loginRequest.email === '<EMAIL>' && loginRequest.password === 'password123') {
      const user: User = {
        id: 2,
        username: 'user',
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'User',
        role: 'user',
        token: 'mock-jwt-token-user'
      };

      this.storeUserData(user, loginRequest.rememberMe);
      return of(user);
    }

    return throwError(() => new Error('Invalid email or password'));
  }

  register(registerRequest: RegisterRequest): Observable<User> {
    // In a real app, this would call the API
    // return this.http.post<User>(`${this.apiUrl}/register`, registerRequest);

    // For demo, we'll simulate a successful registration
    const user: User = {
      id: Math.floor(Math.random() * 1000) + 10,
      username: registerRequest.username,
      email: registerRequest.email,
      firstName: registerRequest.firstName,
      lastName: registerRequest.lastName,
      role: 'user',
      token: 'mock-jwt-token-' + registerRequest.username
    };

    this.storeUserData(user, false);
    return of(user);
  }

  forgotPassword(request: ForgotPasswordRequest): Observable<any> {
    // In a real app, this would call the API
    // return this.http.post(`${this.apiUrl}/forgot-password`, request);

    // For demo, we'll simulate a successful request
    return of({ message: 'Password reset email sent successfully' });
  }

  resetPassword(request: ResetPasswordRequest): Observable<any> {
    // In a real app, this would call the API
    // return this.http.post(`${this.apiUrl}/reset-password`, request);

    // For demo, we'll simulate a successful request
    return of({ message: 'Password reset successfully' });
  }

  logout(): void {
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
    this.router.navigate(['/auth/login']);
  }

  isAuthenticated(): boolean {
    return !!this.currentUserValue;
  }

  refreshToken(): Observable<User> {
    // In a real app, this would call the API to refresh the token
    // const refreshToken = this.currentUserValue?.refreshToken;
    // return this.http.post<User>(`${this.apiUrl}/refresh-token`, { refreshToken })
    //   .pipe(
    //     tap(user => {
    //       this.storeUserData(user, true);
    //     })
    //   );

    // For demo, we'll just return the current user
    if (this.currentUserValue) {
      return of(this.currentUserValue);
    }

    return throwError(() => new Error('No refresh token available'));
  }

  updateUserProfile(user: User): Observable<User> {
    // In a real app, this would call the API
    // return this.http.put<User>(`${this.apiUrl}/profile`, user)
    //   .pipe(
    //     tap(updatedUser => {
    //       this.storeUserData(updatedUser, true);
    //     })
    //   );

    // For demo, we'll just update the stored user
    this.storeUserData(user, true);
    return of(user);
  }

  private storeUserData(user: User, rememberMe: boolean = false): void {
    if (rememberMe) {
      localStorage.setItem('currentUser', JSON.stringify(user));
    } else {
      sessionStorage.setItem('currentUser', JSON.stringify(user));
    }
    this.currentUserSubject.next(user);
  }

  private getUserFromLocalStorage(): User | null {
    // Check session storage first (for session-only login)
    let userJson = sessionStorage.getItem('currentUser');

    // If not in session storage, check local storage (for remember me)
    if (!userJson) {
      userJson = localStorage.getItem('currentUser');
    }

    if (userJson) {
      try {
        return JSON.parse(userJson);
      } catch (e) {
        localStorage.removeItem('currentUser');
        sessionStorage.removeItem('currentUser');
        return null;
      }
    }
    return null;
  }
}
