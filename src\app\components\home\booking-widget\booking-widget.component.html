<div class="booking-widget bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden" @slideIn>
  <!-- Header -->
  <div class="bg-primary-500 text-white p-4 flex justify-between items-center">
    <h3 class="font-heading text-lg font-semibold">Book Your Stay</h3>
    <button (click)="toggleCollapse()" class="md:hidden text-white focus:outline-none">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path *ngIf="isCollapsed" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        <path *ngIf="!isCollapsed" fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>
  
  <!-- Form -->
  <div [class.hidden]="isCollapsed" class="p-4">
    <form class="space-y-4">
      <!-- Check-in Date -->
      <div class="form-group">
        <label for="check-in" class="block text-sm font-medium text-gray-700 mb-1">Check-in Date</label>
        <div class="relative">
          <input 
            type="date" 
            id="check-in" 
            [(ngModel)]="bookingData.checkIn" 
            name="checkIn"
            (blur)="validateInput('checkIn')"
            class="w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            [class.border-red-500]="!validInputs.checkIn && bookingData.checkIn !== ''"
            [class.border-green-500]="validInputs.checkIn && bookingData.checkIn !== ''"
          >
          <div *ngIf="validInputs.checkIn && bookingData.checkIn !== ''" class="absolute right-3 top-3 text-green-500" @checkmarkAnimation>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Check-out Date -->
      <div class="form-group">
        <label for="check-out" class="block text-sm font-medium text-gray-700 mb-1">Check-out Date</label>
        <div class="relative">
          <input 
            type="date" 
            id="check-out" 
            [(ngModel)]="bookingData.checkOut" 
            name="checkOut"
            (blur)="validateInput('checkOut')"
            class="w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            [class.border-red-500]="!validInputs.checkOut && bookingData.checkOut !== ''"
            [class.border-green-500]="validInputs.checkOut && bookingData.checkOut !== ''"
          >
          <div *ngIf="validInputs.checkOut && bookingData.checkOut !== ''" class="absolute right-3 top-3 text-green-500" @checkmarkAnimation>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Guests -->
      <div class="form-group">
        <label for="guests" class="block text-sm font-medium text-gray-700 mb-1">Number of Guests</label>
        <div class="relative">
          <select 
            id="guests" 
            [(ngModel)]="bookingData.guests" 
            name="guests"
            (change)="validateInput('guests')"
            class="w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option [value]="1">1 Guest</option>
            <option [value]="2">2 Guests</option>
            <option [value]="3">3 Guests</option>
            <option [value]="4">4 Guests</option>
            <option [value]="5">5+ Guests</option>
          </select>
        </div>
      </div>
      
      <!-- Room Type -->
      <div class="form-group">
        <label for="roomType" class="block text-sm font-medium text-gray-700 mb-1">Room Type</label>
        <div class="relative">
          <select 
            id="roomType" 
            [(ngModel)]="bookingData.roomType" 
            name="roomType"
            (change)="validateInput('roomType')"
            class="w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option *ngFor="let room of roomTypes" [value]="room.id">{{room.name}}</option>
          </select>
        </div>
      </div>
      
      <!-- Submit Button -->
      <button 
        type="button" 
        (click)="checkAvailability()" 
        class="w-full btn btn-secondary text-background-dark font-medium py-3 rounded-md"
      >
        Check Availability
      </button>
    </form>
  </div>
</div>
