import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-booking-widget',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './booking-widget.component.html',
  styleUrl: './booking-widget.component.css',
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(50px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
    trigger('checkmarkAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
      ]),
    ]),
  ]
})
export class BookingWidgetComponent {
  bookingData = {
    checkIn: '',
    checkOut: '',
    guests: 2,
    roomType: 'standard'
  };
  
  isCollapsed = false;
  validInputs = {
    checkIn: false,
    checkOut: false,
    guests: true,
    roomType: true
  };
  
  roomTypes = [
    { id: 'standard', name: 'Standard Room' },
    { id: 'deluxe', name: 'Deluxe Room' },
    { id: 'suite', name: 'Suite' }
  ];
  
  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
  }
  
  validateInput(field: string) {
    switch(field) {
      case 'checkIn':
        this.validInputs.checkIn = !!this.bookingData.checkIn;
        break;
      case 'checkOut':
        this.validInputs.checkOut = !!this.bookingData.checkOut;
        break;
      case 'guests':
        this.validInputs.guests = this.bookingData.guests > 0;
        break;
      case 'roomType':
        this.validInputs.roomType = !!this.bookingData.roomType;
        break;
    }
  }
  
  checkAvailability() {
    // Validate all inputs
    this.validateInput('checkIn');
    this.validateInput('checkOut');
    this.validateInput('guests');
    this.validateInput('roomType');
    
    // Check if all inputs are valid
    const allValid = Object.values(this.validInputs).every(valid => valid);
    
    if (allValid) {
      console.log('Checking availability for:', this.bookingData);
      // Here you would typically call a service to check availability
      alert('Checking availability for your dates. This would connect to a booking system in a real implementation.');
    } else {
      alert('Please fill in all required fields correctly.');
    }
  }
}
