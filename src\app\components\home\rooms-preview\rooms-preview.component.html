<section class="py-16 md:py-24 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- Section Title -->
    <div class="text-center mb-12" @fadeIn>
      <h2 class="text-3xl md:text-4xl font-heading font-semibold text-primary-500 mb-4">Our Rooms</h2>
      <p class="text-lg text-gray-600 max-w-3xl mx-auto">
        Experience the perfect blend of traditional Mithila aesthetics and modern comfort in our thoughtfully designed rooms.
      </p>
    </div>
    
    <!-- Room Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8" @staggerIn>
      <div *ngFor="let room of rooms" class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
        <!-- Room Image -->
        <div class="relative overflow-hidden h-64">
          <img [src]="room.image" [alt]="room.name" class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
          <div class="absolute top-0 right-0 bg-secondary-500 text-background-dark px-4 py-2 font-medium">
            ${{room.price}}/night
          </div>
        </div>
        
        <!-- Room Details -->
        <div class="p-6">
          <h3 class="text-xl font-heading font-semibold text-gray-900 mb-2">{{room.name}}</h3>
          <p class="text-gray-600 mb-4">{{room.description}}</p>
          
          <!-- Features -->
          <ul class="mb-6 space-y-1">
            <li *ngFor="let feature of room.features" class="flex items-center text-sm text-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{feature}}
            </li>
          </ul>
          
          <!-- View Details Button -->
          <a [routerLink]="['/rooms', room.id]" class="btn btn-outline-secondary w-full text-center">View Details</a>
        </div>
      </div>
    </div>
    
    <!-- View All Rooms Button -->
    <div class="text-center mt-12" @fadeIn>
      <a routerLink="/rooms" class="btn btn-primary inline-flex items-center">
        <span>View All Rooms</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </a>
    </div>
  </div>
</section>
