import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';

@Component({
  selector: 'app-rooms-preview',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './rooms-preview.component.html',
  styleUrl: './rooms-preview.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('200ms', [
            animate('400ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
          ]),
        ], { optional: true }),
      ]),
    ]),
  ]
})
export class RoomsPreviewComponent {
  rooms = [
    {
      id: 'deluxe',
      name: 'Deluxe Room',
      description: 'Spacious room with traditional Mithila decor, king-size bed, and modern amenities.',
      price: 41,
      image: 'https://images.unsplash.com/photo-1590490360182-c33d57733427?q=80&w=1974&auto=format&fit=crop',
      features: ['King-size bed', 'Air conditioning', 'Private bathroom', 'Free Wi-Fi']
    },
    {
      id: 'standard',
      name: 'Standard Room',
      description: 'Comfortable room with twin beds, traditional artwork, and all essential amenities.',
      price: 35,
      image: 'https://images.unsplash.com/photo-1595576508898-0ad5c879a061?q=80&w=1974&auto=format&fit=crop',
      features: ['Twin beds', 'Air conditioning', 'Private bathroom', 'Free Wi-Fi']
    },
    {
      id: 'suite',
      name: 'Mithila Suite',
      description: 'Luxurious suite with separate living area, handcrafted furniture, and premium amenities.',
      price: 65,
      image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?q=80&w=2070&auto=format&fit=crop',
      features: ['King-size bed', 'Separate living area', 'Premium amenities', 'Free breakfast']
    }
  ];
}
