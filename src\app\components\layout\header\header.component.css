/* Header specific styles */
header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 50; /* Ensure it stays above other elements */
}

header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Add a smooth transition effect when opening/closing the mobile menu */
nav {
  transition: all 0.3s ease-in-out;
}
