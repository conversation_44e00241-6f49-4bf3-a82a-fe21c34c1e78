<header class="bg-white shadow-md">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a routerLink="/" class="flex items-center">
          <img src="https://scontent.fktm7-1.fna.fbcdn.net/v/t39.30808-1/385774439_122127428114027028_7855394761270808633_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=2d3e12&_nc_ohc=PB-osu-lkYcQ7kNvwFNzYN2&_nc_oc=AdkotyFZRYEzQacojl3JuEhYCPIpckyjFCIpTmNyP3rVDG6oDSEhjvAfrkRzrZUJoHc&_nc_zt=24&_nc_ht=scontent.fktm7-1.fna&_nc_gid=EuuP8j_Yu48e-8-9amlTDQ&oh=00_AfIyKCZ962bqhJ8sZrexnY9iHICOSm_t3hwK7h1tBIGELw&oe=6834DCDA" alt="Mithilani Ghar Logo" class="h-12 w-auto">
          <span class="ml-3 text-xl font-display font-semibold text-mithila-red">Mithilani Ghar</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/shop" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Shop</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button" (click)="toggleMenu()" class="text-gray-700 hover:text-primary-600 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-4 border-t border-gray-200">
      <nav class="flex flex-col space-y-4">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/shop" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Shop</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>
      </nav>
    </div>
  </div>
</header><header class="bg-white shadow-md fixed top-0 z-50 transition-all duration-300" [ngClass]="{'shadow-lg': scrolled}">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a routerLink="/" class="flex items-center">
           <img src="https://scontent.fktm7-1.fna.fbcdn.net/v/t39.30808-1/385774439_122127428114027028_7855394761270808633_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=2d3e12&_nc_ohc=PB-osu-lkYcQ7kNvwFNzYN2&_nc_oc=AdkotyFZRYEzQacojl3JuEhYCPIpckyjFCIpTmNyP3rVDG6oDSEhjvAfrkRzrZUJoHc&_nc_zt=24&_nc_ht=scontent.fktm7-1.fna&_nc_gid=EuuP8j_Yu48e-8-9amlTDQ&oh=00_AfIyKCZ962bqhJ8sZrexnY9iHICOSm_t3hwK7h1tBIGELw&oe=6834DCDA" alt="Mithilani Ghar Logo" class="h-12 w-auto">
          <span class="ml-3 text-xl font-display font-semibold text-mithila-red">Mithilani Ghar</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-6">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/shop" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Shop</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>
      </nav>

      <!-- Auth Buttons / User Menu (Desktop) -->
      <div class="hidden md:flex items-center space-x-4">
        <!-- Not logged in -->
        <ng-container *ngIf="!authService.isAuthenticated()">
          <a routerLink="/auth/login" class="text-gray-700 hover:text-primary-600 transition-colors duration-200">
            Login
          </a>
          <a routerLink="/auth/register" class="btn btn-primary py-2 px-4 rounded-md text-sm">
            Register
          </a>
        </ng-container>

        <!-- Logged in -->
        <div *ngIf="authService.isAuthenticated()" class="relative">
          <button (click)="toggleUserDropdown()" class="flex items-center space-x-2 focus:outline-none">
            <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 font-medium">
              {{ authService.currentUserValue?.firstName?.charAt(0) || authService.currentUserValue?.username?.charAt(0) || 'U' }}
            </div>
            <span class="text-gray-700">{{ authService.currentUserValue?.firstName || authService.currentUserValue?.username }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Dropdown menu -->
          <div *ngIf="userDropdownOpen" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
            <a routerLink="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              My Profile
            </a>
            <a routerLink="/orders" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              My Orders
            </a>
            <div class="border-t border-gray-100 my-1"></div>
            <button (click)="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Logout
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden flex items-center">
        <!-- Login button for mobile -->
        <a *ngIf="!authService.isAuthenticated()" routerLink="/auth/login" class="mr-4 text-gray-700 hover:text-primary-600">
          <span class="material-icons">login</span>
        </a>

        <!-- User icon for mobile when logged in -->
        <button *ngIf="authService.isAuthenticated()" (click)="toggleUserDropdown()" class="mr-4 text-gray-700 hover:text-primary-600">
          <span class="material-icons">account_circle</span>
        </button>

        <!-- Hamburger menu -->
        <button type="button" (click)="toggleMenu()" class="text-gray-700 hover:text-primary-600 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-4 border-t border-gray-200">
      <nav class="flex flex-col space-y-4">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/shop" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Shop</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>

        <!-- Auth links for mobile -->
        <div class="border-t border-gray-200 pt-4 mt-2">
          <ng-container *ngIf="!authService.isAuthenticated()">
            <a routerLink="/auth/login" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200">
              <span class="material-icons mr-2 text-sm">login</span> Login
            </a>
            <a routerLink="/auth/register" class="flex items-center mt-4 text-gray-700 hover:text-primary-600 transition-colors duration-200">
              <span class="material-icons mr-2 text-sm">person_add</span> Register
            </a>
          </ng-container>

          <ng-container *ngIf="authService.isAuthenticated()">
            <div class="flex items-center mb-4">
              <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 font-medium mr-2">
                {{ authService.currentUserValue?.firstName?.charAt(0) || authService.currentUserValue?.username?.charAt(0) || 'U' }}
              </div>
              <span class="text-gray-700">{{ authService.currentUserValue?.firstName || authService.currentUserValue?.username }}</span>
            </div>
            <a routerLink="/profile" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200">
              <span class="material-icons mr-2 text-sm">person</span> My Profile
            </a>
            <a routerLink="/orders" class="flex items-center mt-4 text-gray-700 hover:text-primary-600 transition-colors duration-200">
              <span class="material-icons mr-2 text-sm">shopping_bag</span> My Orders
            </a>
            <button (click)="logout()" class="flex items-center w-full text-left mt-4 text-gray-700 hover:text-primary-600 transition-colors duration-200">
              <span class="material-icons mr-2 text-sm">logout</span> Logout
            </button>
          </ng-container>
        </div>
      </nav>
    </div>

    <!-- Mobile User Dropdown -->
    <div *ngIf="userDropdownOpen && authService.isAuthenticated()" class="md:hidden py-4 border-t border-gray-200 mt-4">
      <div class="flex flex-col space-y-4">
        <a routerLink="/profile" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200">
          <span class="material-icons mr-2 text-sm">person</span> My Profile
        </a>
        <a routerLink="/orders" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200">
          <span class="material-icons mr-2 text-sm">shopping_bag</span> My Orders
        </a>
        <button (click)="logout()" class="flex items-center w-full text-left text-gray-700 hover:text-primary-600 transition-colors duration-200">
          <span class="material-icons mr-2 text-sm">logout</span> Logout
        </button>
      </div>
    </div>
  </div>
</header>
