import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { TeamMember, TeamMemberServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    // HeroBannerComponent,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    ServiceProxyModule
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.css'
})
export class AboutComponent {
  teamMembers:TeamMember[] =[]
constructor(private _team:TeamMemberServiceProxy){}

ngOnInit() {
  this.loadTeamMembers();
}

loadTeamMembers(){
      this._team.getAllTeamMembers().subscribe((res)=>{
        console.log(res)
        this.teamMembers=res
      })
}
}
