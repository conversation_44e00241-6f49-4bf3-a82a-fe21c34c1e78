import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { Contact, ContactServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    HeroBannerComponent,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    ServiceProxyModule
  ],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('800ms 200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('100ms', [
            animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ])
  ]
})
export class ContactComponent {
  constructor(private _contactService:ContactServiceProxy){}
  datanay: any;

  ngOnInit() {
    this.loadContactForm();
    console.log("Contact Component Loaded");
  }

  loadContactForm() {
    this._contactService.getAllContact().subscribe((result) => {
      console.log(result);
      this.datanay = result;

    });
  }

  contactForm: Contact = new Contact();

  formErrors = {
    name: '',
    email: '',
    subject: '',
    message: ''
  };

  formSubmitted = false;
  formSuccess = false;
  formLoading = false;

  // Social media links
  socialLinks = [
    {
      name: 'Facebook',
      icon: 'fab fa-facebook-f',
      url: 'https://facebook.com/mithilanighar',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: 'Instagram',
      icon: 'fab fa-instagram',
      url: 'https://instagram.com/mithilanighar',
      color: 'bg-pink-600 hover:bg-pink-700'
    },
    {
      name: 'Twitter',
      icon: 'fab fa-twitter',
      url: 'https://twitter.com/mithilanighar',
      color: 'bg-sky-500 hover:bg-sky-600'
    },
    {
      name: 'YouTube',
      icon: 'fab fa-youtube',
      url: 'https://youtube.com/mithilanighar',
      color: 'bg-red-600 hover:bg-red-700'
    }
  ];

  // FAQ items
  faqItems = [
    {
      question: 'What are your opening hours?',
      answer: 'We are open daily from 9:00 AM to 8:00 PM, including holidays.'
    },
    {
      question: 'Do you offer guided tours?',
      answer: 'Yes, we offer guided tours of our gallery and cultural center. Please contact us in advance to schedule a tour.'
    },
    {
      question: 'How can I purchase Mithila art?',
      answer: 'You can purchase art directly from our gallery or through our online shop. We ship worldwide and offer secure payment options.'
    },
    {
      question: 'Do you host cultural events?',
      answer: 'Yes, we regularly host cultural events, workshops, and exhibitions. Check our Events page or contact us for upcoming events.'
    }
  ];

  validateForm(): boolean {
    let isValid = true;
    this.formErrors = {
      name: '',
      email: '',
      subject: '',
      message: ''
    };

    if (!(this.contactForm.name?.trim() ?? '').length) {
      this.formErrors.name = 'Name is required';
      isValid = false;
    }

    if (!(this.contactForm.email?.trim() ?? '').length) {
      this.formErrors.email = 'Email is required';
      isValid = false;
    } else if (!this.isValidEmail(this.contactForm.email ?? '')) {
      this.formErrors.email = 'Please enter a valid email address';
      isValid = false;
    }

    if (!(this.contactForm.subject?.trim() ?? '').length) {
      this.formErrors.subject = 'Subject is required';
      isValid = false;
    }

    if (!(this.contactForm.message?.trim() ?? '').length) {
      this.formErrors.message = 'Message is required';
      isValid = false;
    }

    return isValid;
  }

  isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  submitForm() {
    this.formSubmitted = true;

    if (!this.validateForm()) {
      return;
    }

    this.formLoading = true;

    // Simulate API call
    setTimeout(() => {
      this._contactService.create(this.contactForm).subscribe((result) => {
        console.log(result);
      });
      console.log('Form submitted:', this.contactForm);
      // Here you would typically send the form data to a backend service
      this.formLoading = false;
      this.formSuccess = true;

      // Reset form after 3 seconds
      setTimeout(() => {
        this.formSuccess = false;
        this.formSubmitted = false;
        this.contactForm = new Contact();
      }, 3000);
    }, 1500);
  }
}
