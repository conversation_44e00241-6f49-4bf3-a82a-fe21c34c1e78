<!-- Enhanced <PERSON> Banner with <PERSON><PERSON>la Art Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg')">
  </div>

  <!-- Overlay Gradient -->
  <div class="absolute inset-0 bg-gradient-to-b from-gray-900/60 via-gray-900/40 to-gray-900/70"></div>

  <!-- Decorative Art Background -->
  <app-mithila-art-background
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [opacity]="'0.15'"
  ></app-mithila-art-background>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      Events & Workshops
    </h1>

    <!-- Subtitle -->
    <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-8 animate-fade-in delay-300">
      Immerse yourself in the rich cultural heritage of Mithila through our workshops, exhibitions, and cultural events
    </p>

    <!-- CTA Button -->
    <div class="flex flex-wrap justify-center gap-4 animate-fade-in delay-500">
      <button (click)="setActiveCategory('all')" class="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-full transition-colors duration-300 flex items-center">
        View All Events
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [position]="'bottom'"
    [height]="'30px'"
  ></app-mithila-border>
</div>

<!-- Event Categories Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#C1440E"
  backgroundGradient="from-secondary-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Explore Our Events"
    subtitle="Discover the diverse range of events and activities at Mithilani Ghar"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12" @staggerIn>
    <div *ngFor="let category of eventCategories; let i = index" class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" @fadeIn>
      <!-- Decorative Border -->
      <div class="absolute -inset-1 bg-gradient-to-br rounded-lg blur-sm opacity-30 group-hover:opacity-100 transition-opacity duration-300"
           [ngStyle]="{'background-image': 'linear-gradient(to bottom right, ' + category.color + '80, ' + category.color + '40, ' + category.color + '80)'}">
      </div>

      <div class="relative rounded-lg overflow-hidden">
        <!-- Image -->
        <div class="relative h-64 overflow-hidden">
          <img [src]="category.image" [alt]="category.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

          <!-- Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

          <!-- Category Name -->
          <div class="absolute inset-0 flex flex-col justify-end p-6">
            <h3 class="text-2xl font-bold text-white mb-2 drop-shadow-md">{{category.name}}</h3>
            <p class="text-white/90 text-sm mb-4 line-clamp-2 drop-shadow-md">{{category.description}}</p>

            <!-- Icon -->
            <div class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm p-2 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="category.name" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Button -->
        <div class="p-4 bg-white/90 backdrop-blur-sm">
          <button (click)="setActiveCategory(category.code || '')" class="w-full py-3 rounded-full font-medium transition-all duration-300 flex items-center justify-center"
                  [ngClass]="activeCategory === category.code ? 'bg-opacity-100 text-white' : 'bg-opacity-10 hover:bg-opacity-20 text-gray-800 hover:text-gray-900'"
                  [ngStyle]="{'background-color': activeCategory === category.code ? category.color : category.color + '20'}">
            Explore {{category.name}}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Upcoming Events Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-success-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Upcoming Events"
    subtitle="Join us for these exciting events at Mithilani Ghar"
  ></app-section-title>

  <!-- Filter Buttons -->
  <div class="flex flex-wrap justify-center gap-3 mt-8 mb-12">
    <button (click)="setActiveCategory('all')"
            class="px-5 py-2 rounded-full font-medium transition-all duration-300"
            [ngClass]="activeCategory === 'all' ? 'bg-accent-600 text-white' : 'bg-accent-100 text-accent-600 hover:bg-accent-200'">
      All Events
    </button>
    <button *ngFor="let category of eventCategories"
            (click)="setActiveCategory(category.code || '')"
            class="px-5 py-2 rounded-full font-medium transition-all duration-300"
            [ngClass]="activeCategory === category.code ? 'bg-accent-600 text-white' : 'bg-accent-100 text-accent-600 hover:bg-accent-200'">
      {{category.name}}
    </button>
  </div>

  <!-- Events Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" @staggerIn>
    <div *ngFor="let event of filteredEvents; let i = index" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group" @fadeIn>
      <div class="relative overflow-hidden h-48">
        <!-- Event Image -->
        <img [src]="event.image" [alt]="event.title" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

        <!-- Event Type Badge -->
        <div class="absolute top-4 left-4">
          <span class="inline-block px-3 py-1 bg-white/80 backdrop-blur-sm text-gray-800 rounded-full text-xs font-bold">
            {{event.eventType}}
          </span>
        </div>

        <!-- Date Badge -->
        <div class="absolute top-4 right-4">
          <span class="inline-block px-3 py-1 bg-primary-500/80 backdrop-blur-sm text-white rounded-full text-xs font-bold">
            {{event.date}}
          </span>
        </div>

        <!-- Event Title -->
        <div class="absolute bottom-0 left-0 right-0 p-4">
          <h3 class="text-xl font-bold text-white drop-shadow-md line-clamp-2">{{event.title}}</h3>
        </div>
      </div>

      <div class="p-6 relative">
        <!-- Location and Time -->
        <div class="flex items-start gap-1 text-sm text-gray-600 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>{{event.location}}</span>
        </div>

        <!-- Time if available -->
        <div *ngIf="event.time" class="flex items-start gap-1 text-sm text-gray-600 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{event.time}}</span>
        </div>

        <!-- Description -->
        <p class="text-gray-700 line-clamp-3 mb-4">{{event.description}}</p>

        <!-- Details Button -->
        <button class="inline-flex items-center px-5 py-2 bg-accent-100 text-accent-600 rounded-full font-medium hover:bg-accent-200 transition-colors duration-300">
          View Details
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </button>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#264653'"
          [secondaryColor]="'#3B945E'"
          [type]="i % 3 === 0 ? 'lotus' : i % 3 === 1 ? 'fish' : 'peacock'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="30px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Regular Classes Section -->
<app-mithila-section
  primaryColor="#E76F51"
  secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Regular Classes"
    subtitle="Join our ongoing classes to develop your Mithila art skills"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12" @staggerIn>
    <div *ngFor="let class of regularClasses; let i = index" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group" @fadeIn>
      <div class="relative overflow-hidden h-48">
        <!-- Class Image -->
        <img [src]="class.image" [alt]="class.title" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

        <!-- Price Badge -->
        <div class="absolute top-4 right-4">
          <span class="inline-block px-3 py-1 bg-brick-500/80 backdrop-blur-sm text-white rounded-full text-xs font-bold">
            Rs. {{class.monthlyFee}}/month
          </span>
        </div>

        <!-- Class Title -->
        <div class="absolute bottom-0 left-0 right-0 p-4">
          <h3 class="text-xl font-bold text-white drop-shadow-md line-clamp-2">{{class.title}}</h3>
        </div>
      </div>

      <div class="p-6 relative">
        <!-- Schedule -->
        <div class="flex items-start gap-1 text-sm text-gray-600 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-brick-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>{{class.schedule}}</span>
        </div>

        <!-- Instructor -->
        <div class="flex items-start gap-1 text-sm text-gray-600 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-brick-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span>Instructor: {{class.instructor}}</span>
        </div>

        <!-- Location -->
        <div class="flex items-start gap-1 text-sm text-gray-600 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-brick-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>{{class.location}}</span>
        </div>

        <!-- Description -->
        <p class="text-gray-700 line-clamp-3 mb-4">{{class.description}}</p>

        <!-- Enroll Button -->
        <button class="inline-flex items-center px-5 py-2 bg-brick-100 text-brick-600 rounded-full font-medium hover:bg-brick-200 transition-colors duration-300">
          Enroll Now
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </button>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#E76F51'"
          [secondaryColor]="'#C1440E'"
          [type]="i === 0 ? 'lotus' : i === 1 ? 'geometric' : 'fish'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="30px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Calendar View Section -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#D81B60"
  backgroundGradient="from-peacock-50 via-background-light to-magenta-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Events Calendar"
    subtitle="Plan your visit with our upcoming events calendar"
  ></app-section-title>

  <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-6 md:p-8 mt-12">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-2xl font-bold text-gray-900">{{currentMonth}} {{currentYear}}</h3>
      <div class="flex gap-2">
        <button class="p-2 rounded-full bg-peacock-100 text-peacock-600 hover:bg-peacock-200 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button class="p-2 rounded-full bg-peacock-100 text-peacock-600 hover:bg-peacock-200 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Calendar Events List -->
    <div class="space-y-4">
      <div *ngFor="let event of allEvents; let i = index" class="flex flex-col md:flex-row gap-4 p-4 rounded-lg hover:bg-peacock-50 transition-colors duration-300">
        <!-- Date Column -->
        <div class="flex-shrink-0 w-full md:w-32 flex flex-row md:flex-col items-center md:items-start gap-2 md:gap-0">
          <div class="bg-peacock-100 text-peacock-800 px-3 py-1 rounded-full text-sm font-medium">
            {{event.date}}
          </div>
          <div *ngIf="event.time" class="text-sm text-gray-600">
            {{event.time}}
          </div>
        </div>

        <!-- Event Details -->
        <div class="flex-grow">
          <div class="flex items-center gap-2 mb-1">
            <span class="inline-block w-3 h-3 rounded-full"
                  [ngStyle]="{'background-color': event.category === 'workshops' ? '#C1440E' :
                                                event.category === 'exhibitions' ? '#F4B400' :
                                                event.category === 'cultural' ? '#3B945E' : '#E76F51'}">
            </span>
            <span class="text-sm font-medium text-gray-600">{{event.eventType}}</span>
          </div>
          <h4 class="text-lg font-semibold text-gray-900">{{event.title}}</h4>
          <p class="text-sm text-gray-700 mt-1">{{event.location}}</p>
        </div>

        <!-- Action Button -->
        <div class="flex-shrink-0 flex items-center">
          <button class="inline-flex items-center px-4 py-1.5 bg-peacock-100 text-peacock-600 rounded-full text-sm font-medium hover:bg-peacock-200 transition-colors duration-300">
            Details
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Registration CTA Section -->
<app-mithila-section
  primaryColor="#F7A700"
  secondaryColor="#3B945E"
  backgroundGradient="from-turmeric-50 via-background-light to-success-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="relative overflow-hidden rounded-xl shadow-2xl">
    <!-- Background Image -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
         style="background-image: url('https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg')">
    </div>

    <!-- Overlay Gradient -->
    <div class="absolute inset-0 bg-gradient-to-r from-gray-900/80 via-gray-900/60 to-gray-900/30"></div>

    <!-- Content -->
    <div class="relative z-10 p-8 md:p-12 lg:p-16 flex flex-col items-center text-center">
      <app-mithila-decorative-element
        [primaryColor]="'#F7A700'"
        [secondaryColor]="'#3B945E'"
        [type]="'lotus'"
        position="relative mx-auto mb-6"
        classes="opacity-90"
        size="60px">
      </app-mithila-decorative-element>

      <h2 class="text-3xl font-bold mb-4 text-white drop-shadow-md">Join Our Upcoming Events</h2>
      <p class="mb-8 text-white font-medium text-lg drop-shadow-md max-w-2xl mx-auto">
        Register for our workshops, exhibitions, and cultural events to immerse yourself in the rich tradition of Mithila art. Limited spaces available for workshops - book early to avoid disappointment.
      </p>

      <div class="flex flex-wrap justify-center gap-4">
        <button class="px-6 py-3 bg-turmeric-500 hover:bg-turmeric-600 text-white font-medium rounded-full transition-colors duration-300 flex items-center shadow-lg">
          Register for Events
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </button>
        <button class="px-6 py-3 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-medium rounded-full transition-colors duration-300 flex items-center shadow-lg">
          Contact Us
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</app-mithila-section>
