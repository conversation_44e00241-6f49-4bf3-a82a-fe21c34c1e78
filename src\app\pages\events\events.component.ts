import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { Category, CategoryServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-events',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    ServiceProxyModule
  ],
  templateUrl: './events.component.html',
  styleUrl: './events.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('200ms', [
            animate('400ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
          ]),
        ], { optional: true }),
      ]),
    ]),
  ]
})
export class EventsComponent {

  eventCategories:Category[]=[];
  constructor(private _category:CategoryServiceProxy ){}


  loadCategpryData(){
    this._category.getAllCategory().subscribe((res)=>{
      console.log(res);
      this.eventCategories=res;

    })
  }

OnInit(){
  this.loadCategpryData()
}

  // Upcomi
  // ng workshops
  upcomingWorkshops = [
    {
      id: 1,
      title: 'Introduction to Mithila Art',
      artist: 'Sarita Devi',
      artistId: '1',
      date: 'June 15-16, 2024',
      time: '10:00 AM - 4:00 PM',
      location: 'Main Workshop, Mithilani Ghar',
      description: 'A beginner-friendly workshop covering the basic techniques, motifs, and cultural context of traditional Mithila painting.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      price: 2500,
      spaces: 15,
      spacesLeft: 8,
      category: 'workshops'
    },
    {
      id: 2,
      title: 'Contemporary Mithila Techniques',
      artist: 'Ramesh Kumar',
      artistId: '2',
      date: 'July 8-10, 2024',
      time: '1:00 PM - 5:00 PM',
      location: 'Studio 2, Mithilani Ghar',
      description: 'Explore innovative approaches to Mithila art, including mixed media techniques and contemporary themes while respecting traditional aesthetics.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      price: 3500,
      spaces: 12,
      spacesLeft: 5,
      category: 'workshops'
    },
    {
      id: 3,
      title: 'Mythological Themes in Mithila Art',
      artist: 'Anita Jha',
      artistId: '3',
      date: 'August 20-22, 2024',
      time: '9:00 AM - 3:00 PM',
      location: 'Main Workshop, Mithilani Ghar',
      description: 'Learn to depict Hindu deities and mythological narratives using traditional Mithila painting techniques and symbolism.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      price: 3000,
      spaces: 15,
      spacesLeft: 10,
      category: 'workshops'
    }
  ];

  // Upcoming exhibitions
  upcomingExhibitions = [
    {
      id: 4,
      title: 'Traditions Reimagined',
      dates: 'June 15 - July 30, 2024',
      location: 'Main Gallery, Mithilani Ghar',
      description: 'Exploring how contemporary artists are reinterpreting traditional Mithila art forms for modern audiences. This exhibition features works from both established masters and emerging talents who are pushing the boundaries of this ancient art form while honoring its cultural roots.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      artists: ['Sarita Devi', 'Ramesh Kumar', 'Anita Jha'],
      openingReception: 'June 15, 2024, 6:00 PM - 9:00 PM',
      category: 'exhibitions'
    },
    {
      id: 5,
      title: 'Sacred Visions',
      dates: 'August 10 - September 25, 2024',
      location: 'Special Exhibition Hall, Mithilani Ghar',
      description: 'A collection of religious and spiritual artwork showcasing the divine in Mithila artistic tradition. This exhibition explores the rich iconography and symbolism used to represent deities, myths, and spiritual concepts in the Mithila painting tradition.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      artists: ['Anita Jha', 'Sunil Yadav'],
      openingReception: 'August 10, 2024, 5:30 PM - 8:30 PM',
      category: 'exhibitions'
    },
    {
      id: 6,
      title: 'Nature\'s Palette',
      dates: 'October 5 - November 20, 2024',
      location: 'Garden Gallery, Mithilani Ghar',
      description: 'Celebrating the natural world through the distinctive style and techniques of Mithila art. This exhibition showcases how artists interpret flora, fauna, and landscapes using the unique visual language of Mithila painting.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      artists: ['Sunil Yadav', 'Sarita Devi'],
      openingReception: 'October 5, 2024, 6:00 PM - 8:00 PM',
      category: 'exhibitions'
    }
  ];

  // Cultural events
  culturalEvents = [
    {
      id: 7,
      title: 'Mithila Cultural Festival',
      date: 'September 15, 2024',
      time: '11:00 AM - 8:00 PM',
      location: 'Mithilani Ghar Courtyard',
      description: 'A day-long celebration of Mithila culture featuring traditional music, dance performances, food, and art demonstrations. Join us for this immersive experience of the rich cultural heritage of the Mithila region.',
      image: 'https://i.etsystatic.com/43638819/r/il/7a099c/5978434699/il_794xN.5978434699_np8t.jpg',
      category: 'cultural'
    },
    {
      id: 8,
      title: 'Artist Talk: Preserving Heritage',
      date: 'July 20, 2024',
      time: '4:00 PM - 6:00 PM',
      location: 'Lecture Hall, Mithilani Ghar',
      description: 'Join us for an insightful discussion on preserving cultural heritage through art. Master artist Sarita Devi will share her experiences and insights on the importance of maintaining traditional art forms in the modern world.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      category: 'cultural'
    },
    {
      id: 9,
      title: 'Documentary Screening: The Art of Mithila',
      date: 'August 5, 2024',
      time: '6:00 PM - 8:30 PM',
      location: 'Media Room, Mithilani Ghar',
      description: 'A special screening of the award-winning documentary that explores the history, techniques, and cultural significance of Mithila art. The screening will be followed by a Q&A session with the filmmaker and featured artists.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      category: 'cultural'
    }
  ];

  // Regular classes
  regularClasses = [
    {
      id: 10,
      title: 'Weekly Mithila Art Class for Beginners',
      schedule: 'Every Saturday, 10:00 AM - 12:00 PM',
      instructor: 'Sarita Devi',
      location: 'Studio 1, Mithilani Ghar',
      description: 'A weekly class designed for beginners to learn the fundamentals of Mithila art. Each session builds upon previous lessons, allowing students to develop their skills progressively.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      monthlyFee: 4000,
      category: 'classes'
    },
    {
      id: 11,
      title: 'Advanced Techniques in Mithila Painting',
      schedule: 'Every Sunday, 2:00 PM - 5:00 PM',
      instructor: 'Ramesh Kumar',
      location: 'Studio 2, Mithilani Ghar',
      description: 'For intermediate and advanced artists looking to refine their skills and explore more complex techniques in Mithila painting. This class focuses on composition, color theory, and advanced motifs.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      monthlyFee: 5500,
      category: 'classes'
    },
    {
      id: 12,
      title: 'Children\'s Mithila Art Class',
      schedule: 'Every Wednesday, 4:00 PM - 5:30 PM',
      instructor: 'Anita Jha',
      location: 'Children\'s Studio, Mithilani Ghar',
      description: 'A fun and educational class designed specifically for children ages 8-14. Students will learn basic Mithila art techniques while exploring their creativity in a supportive environment.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      monthlyFee: 3000,
      category: 'classes'
    }
  ];

  // Combine all events for the calendar view
  get allEvents() {
    return [
      ...this.upcomingWorkshops.map(w => ({
        ...w,
        eventType: 'Workshop'
      })),
      ...this.upcomingExhibitions.map(e => ({
        ...e,
        eventType: 'Exhibition',
        date: e.dates.split(' - ')[0],
        time: e.openingReception ? e.openingReception.split(', ')[1] : ''
      })),
      ...this.culturalEvents.map(c => ({
        ...c,
        eventType: 'Cultural Event'
      }))
    ].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  // Filter functionality
  activeCategory: string = 'all';

  setActiveCategory(category: string) {
    this.activeCategory = category;
  }

  get filteredEvents() {
    if (this.activeCategory === 'all') {
      return this.allEvents;
    }
    return this.allEvents.filter(event =>
      event.category === this.activeCategory
    );
  }

  // Get current month and year for calendar display
  currentMonth: string = new Date().toLocaleString('default', { month: 'long' });
  currentYear: number = new Date().getFullYear();
}
