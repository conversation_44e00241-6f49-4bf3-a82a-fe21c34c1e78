<!-- Enhanced <PERSON> Banner with <PERSON><PERSON>la Art Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Dark Overlay -->
  <div class="absolute inset-0 bg-black bg-opacity-50"></div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'full'"
    position="top-8 left-8 right-8 bottom-8">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      Mithila Art Shop
    </h1>

    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Browse our curated collection of authentic Mithila artwork and bring home a piece of this rich cultural heritage
    </p>

    <!-- Search Bar -->
    <div class="w-full max-w-2xl mx-auto mb-8">
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Search for artwork, artists, or styles..."
          class="w-full px-5 py-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        >
        <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Categories Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Shop by Category"
    subtitle="Explore our diverse collection of Mithila art forms"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let category of categories" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Category Image -->
        <img [src]="category.image" [alt]="category.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>

        <!-- Category Info -->
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h3 class="text-2xl font-bold mb-2">{{category.name}}</h3>
          <p class="text-white/80 mb-4 text-sm">{{category.description}}</p>
          <div class="flex justify-between items-center">
            <span class="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">{{category.count}} items</span>
            <button
              (click)="setActiveCategory(category.id)"
              class="px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors duration-300 text-sm font-medium"
              [style.border]="'1px solid ' + category.color">
              Browse
            </button>
          </div>
        </div>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="category.color || '#C1440E'"
          [secondaryColor]="'#FFFFFF'"
          [type]="category.id === 'paintings' ? 'lotus' : category.id === 'prints' ? 'geometric' : category.id === 'textiles' ? 'peacock' : 'fish'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="30px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Featured Products Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Featured Products"
    subtitle="Discover our curated collection of exceptional Mithila art pieces"
  ></app-section-title>

  <!-- Filter and Sort Controls -->
  <div class="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
    <div class="flex flex-wrap gap-2">
      <button
        (click)="setActiveCategory('all')"
        class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300"
        [ngClass]="activeCategory === 'all' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
        All
      </button>
      <button
        *ngFor="let category of categories"
        (click)="setActiveCategory(category.id || '')"
        class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300"
        [ngClass]="activeCategory === category.id ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
        {{category.name}}
      </button>
    </div>

    <div class="relative">
      <select
        [(ngModel)]="sortOption"
        class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
        <option value="featured">Featured</option>
        <option value="price-low">Price: Low to High</option>
        <option value="price-high">Price: High to Low</option>
        <option value="newest">Newest</option>
        <option value="rating">Top Rated</option>
      </select>
      <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
        </svg>
      </div>
    </div>
  </div>

  <!-- Product Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    <div *ngFor="let product of filteredProducts" class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Product Image -->
        <img [src]="product.image" [alt]="product.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Sale Badge -->
        <div *ngIf="product.salePrice" class="absolute top-4 left-4 bg-accent-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          SALE
        </div>

        <!-- New Badge -->
        <div *ngIf="product.isNew" class="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          NEW
        </div>

        <!-- Quick View Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button class="bg-white text-primary-600 px-4 py-2 rounded-full font-medium hover:bg-primary-50 transition-colors duration-300">
            Quick View
          </button>
        </div>
      </div>

      <div class="p-6">
        <div class="flex justify-between items-start mb-2">
          <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{product.name}}</h3>
          <div class="flex items-center">
            <span class="text-yellow-500">★</span>
            <span class="ml-1 text-sm text-gray-600">{{product.rating}}</span>
          </div>
        </div>

        <p class="text-primary-600 mb-3">By {{product.artist}}</p>

        <p class="text-gray-700 mb-4 text-sm line-clamp-2">{{product.description}}</p>

        <div class="flex justify-between items-center">
          <div>
            <span *ngIf="product.salePrice" class="text-gray-500 line-through mr-2">${{product.price}}</span>
            <span class="text-xl font-bold text-gray-900">${{product.salePrice || product.price}}</span>
          </div>

          <button
            (click)="addToCart(product.id)"
            class="bg-primary-600 text-white px-4 py-2 rounded-full hover:bg-primary-700 transition-colors duration-300">
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Best Sellers Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Best Sellers"
    subtitle="Our most popular and highly rated Mithila art pieces"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let product of bestSellers" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Product Image -->
        <img [src]="product.image" [alt]="product.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Rating Badge -->
        <div class="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold flex items-center">
          <span class="mr-1">★</span>
          <span>{{product.rating}}</span>
        </div>

        <!-- Product Info Overlay -->
        <div class="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
          <button
            (click)="addToCart(product.id)"
            class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors duration-300">
            Add to Cart
          </button>
        </div>
      </div>

      <div class="p-4">
        <h3 class="text-lg font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{product.name}}</h3>
        <p class="text-primary-600 text-sm">By {{product.artist}}</p>
        <div class="flex justify-between items-center mt-2">
          <span class="font-bold text-gray-900">${{product.salePrice || product.price}}</span>
          <span class="text-sm text-gray-600">{{product.reviewCount}} reviews</span>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Shopping Experience CTA -->
<app-mithila-section
  primaryColor="#E76F51"
  secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto">
    <div class="bg-gradient-to-r from-brick-700 to-primary-700 rounded-lg p-8 shadow-lg text-white relative overflow-hidden">
      <!-- Dark Overlay for Better Text Visibility -->
      <div class="absolute inset-0 bg-black/30"></div>

      <!-- Decorative Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <app-mithila-art-background
          [primaryColor]="'#FFFFFF'"
          [secondaryColor]="'#FFFFFF'"
          opacity="5">
        </app-mithila-art-background>
      </div>

      <div class="relative z-10 text-center">
        <app-mithila-decorative-element
          [primaryColor]="'#E76F51'"
          [secondaryColor]="'#C1440E'"
          [type]="'lotus'"
          position="relative mx-auto mb-6"
          classes="opacity-90"
          size="60px">
        </app-mithila-decorative-element>

        <h2 class="text-3xl font-bold mb-4 text-white drop-shadow-md">Authentic Mithila Art for Your Home</h2>
        <p class="mb-8 text-white font-medium text-lg drop-shadow-md max-w-2xl mx-auto">
          Each piece in our collection is handcrafted by skilled Mithila artists using traditional techniques passed down through generations. Bring home a piece of this rich cultural heritage and support the preservation of this ancient art form.
        </p>

        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a routerLink="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">Contact Us</a>
          <button (click)="setActiveCategory('all')" class="btn bg-primary-600 text-white border border-white hover:bg-primary-700 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">Browse All</button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- New Arrivals Section -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="New Arrivals"
    subtitle="The latest additions to our Mithila art collection"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let product of newArrivals" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Product Image -->
        <img [src]="product.image" [alt]="product.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- New Badge -->
        <div class="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          NEW
        </div>

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Quick Add Button -->
        <div class="absolute bottom-4 left-0 right-0 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            (click)="addToCart(product.id)"
            class="bg-white text-primary-600 px-4 py-2 rounded-full font-medium hover:bg-primary-50 transition-colors duration-300 shadow-lg">
            Quick Add
          </button>
        </div>
      </div>

      <div class="p-4">
        <h3 class="text-lg font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{product.name}}</h3>
        <p class="text-primary-600 text-sm">By {{product.artist}}</p>
        <div class="flex justify-between items-center mt-2">
          <span class="font-bold text-gray-900">${{product.salePrice || product.price}}</span>
          <div class="flex items-center">
            <span class="text-yellow-500">★</span>
            <span class="ml-1 text-sm text-gray-600">{{product.rating}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Special Offers Section -->
<app-mithila-section
  *ngIf="onSale.length > 0"
  primaryColor="#C1440E"
  secondaryColor="#E76F51"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Special Offers"
    subtitle="Limited-time discounts on select Mithila art pieces"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let product of onSale" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Product Image -->
        <img [src]="product.image" [alt]="product.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Sale Badge -->
        <div class="absolute top-4 left-4 bg-accent-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          SALE
        </div>

        <!-- Discount Percentage -->
        <div class="absolute top-4 right-4 bg-white text-accent-600 px-3 py-1 rounded-full text-sm font-bold">
          <ng-container *ngIf="product.price != null && product.salePrice != null">
            {{((product.price - product.salePrice) / product.price * 100).toFixed(0)}}% OFF
          </ng-container>
        </div>

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <div class="p-4">
        <h3 class="text-lg font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{product.name}}</h3>
        <p class="text-primary-600 text-sm">By {{product.artist}}</p>
        <div class="flex justify-between items-center mt-2">
          <div>
            <span class="text-gray-500 line-through mr-2">${{product.price}}</span>
            <span class="font-bold text-accent-600">${{product.salePrice}}</span>
          </div>
          <button
            (click)="addToCart(product.id)"
            class="bg-accent-600 text-white px-3 py-1 rounded-full hover:bg-accent-700 transition-colors duration-300 text-sm">
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Shopping Cart Floating Button -->
<div class="fixed bottom-8 right-8 z-50">
  <button
    class="bg-primary-600 text-white w-16 h-16 rounded-full shadow-lg hover:bg-primary-700 hover:shadow-xl transition-all duration-300 flex items-center justify-center relative group">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
    </svg>
    <span *ngIf="cartItems > 0" class="absolute -top-2 -right-2 bg-accent-600 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center">
      {{cartItems}}
    </span>
    <span class="absolute top-0 left-0 right-0 bottom-0 bg-primary-500 rounded-full animate-ping opacity-30 group-hover:opacity-50"></span>
  </button>
</div>
