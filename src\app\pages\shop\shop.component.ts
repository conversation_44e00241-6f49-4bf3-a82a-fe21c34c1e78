import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Category, CategoryServiceProxy, Product, ProductServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    ServiceProxyModule
  ],
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.css'
})
export class ShopComponent {
  featuredProducts:Product[] =[];
  categories: Category[] =[];

  constructor(private _productService:ProductServiceProxy,private _categoryService:CategoryServiceProxy){}
  ngOnInit(): void {
    this.getProducts();
    this.getCategories();
  }
  // Product categories
  getCategories(){
    this._categoryService.getAllCategory().subscribe((res:any)=>{
      this.categories = res;
    })
  }


  // Featured products
  getProducts(){
    this._productService.getAllProducts().subscribe((res:any)=>{
      this.featuredProducts = res;
    })
  }



  // Best selling products (subset of featured products)
  get bestSellers() {
    return this.featuredProducts
      .filter(product => product.reviewCount > 10 && product.rating >= 4.7)
      .sort((a, b) => b.reviewCount - a.reviewCount)
      .slice(0, 4);
  }

  // New arrivals (subset of featured products)
  get newArrivals() {
    return this.featuredProducts
      .filter(product => product.isNew)
      .slice(0, 4);
  }

  // Sale products (subset of featured products)
  get onSale() {
    return this.featuredProducts
      .filter(product => product.salePrice !== null)
      .slice(0, 4);
  }

  // Filter and sort functionality
  activeCategory: string = 'all';
  sortOption: string = 'featured';
  searchQuery: string = '';

  setActiveCategory(category: string) {
    this.activeCategory = category;
  }

  setSortOption(option: string) {
    this.sortOption = option;
  }

  get filteredProducts() {
    let filtered = this.featuredProducts;

    // Apply category filter
    if (this.activeCategory !== 'all') {
      filtered = filtered.filter(product =>
        product.category && product.category.toLowerCase() === this.activeCategory.toLowerCase()
      );
    }

    // Apply search filter if there's a query
    if (this.searchQuery.trim() !== '') {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        (product.artist && product.artist.toLowerCase().includes(query)) ||
        (product.description && product.description.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    switch (this.sortOption) {
      case 'price-low':
        return filtered.sort((a, b) =>
          (a.salePrice || a.price) - (b.salePrice || b.price)
        );
      case 'price-high':
        return filtered.sort((a, b) =>
          (b.salePrice || b.price) - (a.salePrice || a.price)
        );
      case 'newest':
        return filtered.filter(p => p.isNew).concat(filtered.filter(p => !p.isNew));
      case 'rating':
        return filtered.sort((a, b) => b.rating - a.rating);
      case 'featured':
      default:
        return filtered;
    }
  }

  // Shopping cart functionality (simplified)
  cartItems: number = 0;

  addToCart(productId: number) {
    this.cartItems++;
    // In a real app, this would add the product to a cart service
    console.log(`Added product ${productId} to cart`);
  }
}
