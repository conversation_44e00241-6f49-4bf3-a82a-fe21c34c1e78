import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.css'
})
export class ShopComponent {
  // Product categories
  categories = [
    {
      id: 'paintings',
      name: 'Paintings',
      description: 'Original Mithila paintings created by master artists using traditional techniques.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      color: '#C1440E',
      count: 12
    },
    {
      id: 'prints',
      name: 'Art Prints',
      description: 'High-quality reproductions of original Mithila artwork, perfect for home decor.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      color: '#F4B400',
      count: 18
    },
    {
      id: 'textiles',
      name: 'Textiles',
      description: 'Handcrafted textiles featuring Mithila designs, including scarves, tapestries, and more.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      color: '#264653',
      count: 8
    },
    {
      id: 'crafts',
      name: 'Crafts & Decor',
      description: 'Decorative items and crafts inspired by Mithila art traditions.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      color: '#3B945E',
      count: 15
    }
  ];

  // Featured products
  featuredProducts = [
    {
      id: 1,
      name: 'Village Celebration',
      artist: 'Sarita Devi',
      category: 'Paintings',
      price: 450,
      salePrice: null,
      description: 'An original Mithila painting depicting a traditional village celebration with intricate details and vibrant colors.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural pigments on handmade paper',
      inStock: true,
      isNew: true,
      isFeatured: true,
      rating: 4.9,
      reviewCount: 12
    },
    {
      id: 2,
      name: 'Geometric Harmony',
      artist: 'Ramesh Kumar',
      category: 'Prints',
      price: 85,
      salePrice: 68,
      description: 'A high-quality print of a contemporary Mithila artwork featuring geometric patterns in traditional colors.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '18 × 24 inches',
      medium: 'Archival print on acid-free paper',
      inStock: true,
      isNew: false,
      isFeatured: true,
      rating: 4.7,
      reviewCount: 28
    },
    {
      id: 3,
      name: 'Durga Silk Scarf',
      artist: 'Anita Jha',
      category: 'Textiles',
      price: 120,
      salePrice: null,
      description: 'A luxurious silk scarf featuring a Mithila-inspired design of the goddess Durga in rich, vibrant colors.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      dimensions: '36 × 36 inches',
      medium: '100% pure silk',
      inStock: true,
      isNew: true,
      isFeatured: true,
      rating: 5.0,
      reviewCount: 9
    },
    {
      id: 4,
      name: 'Peacock Wall Hanging',
      artist: 'Sunil Yadav',
      category: 'Crafts & Decor',
      price: 175,
      salePrice: 140,
      description: 'A handcrafted wooden wall hanging featuring a peacock design painted in traditional Mithila style.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      dimensions: '12 × 18 inches',
      medium: 'Acrylic on carved wood',
      inStock: true,
      isNew: false,
      isFeatured: true,
      rating: 4.8,
      reviewCount: 15
    },
    {
      id: 5,
      name: 'Wedding Ceremony',
      artist: 'Sarita Devi',
      category: 'Paintings',
      price: 550,
      salePrice: null,
      description: 'An original Mithila painting depicting a traditional wedding ceremony with elaborate details and symbolism.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      dimensions: '30 × 40 inches',
      medium: 'Natural pigments on handmade paper',
      inStock: true,
      isNew: false,
      isFeatured: true,
      rating: 4.9,
      reviewCount: 7
    },
    {
      id: 6,
      name: 'Fish Motif Cushion Cover',
      artist: 'Anita Jha',
      category: 'Textiles',
      price: 45,
      salePrice: 36,
      description: 'A handcrafted cotton cushion cover featuring traditional Mithila fish motifs in indigo blue and terracotta.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '18 × 18 inches',
      medium: '100% cotton with embroidery',
      inStock: true,
      isNew: true,
      isFeatured: true,
      rating: 4.6,
      reviewCount: 23
    },
    {
      id: 7,
      name: 'Elephant Family',
      artist: 'Ramesh Kumar',
      category: 'Prints',
      price: 95,
      salePrice: null,
      description: 'A limited edition print featuring a family of elephants decorated in traditional Mithila patterns.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      dimensions: '16 × 20 inches',
      medium: 'Giclee print on museum-quality paper',
      inStock: false,
      isNew: false,
      isFeatured: true,
      rating: 4.8,
      reviewCount: 18
    },
    {
      id: 8,
      name: 'Lotus Ceramic Plate',
      artist: 'Sunil Yadav',
      category: 'Crafts & Decor',
      price: 65,
      salePrice: null,
      description: 'A hand-painted ceramic decorative plate featuring a lotus design in traditional Mithila style.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      dimensions: '10 inch diameter',
      medium: 'Hand-painted ceramic',
      inStock: true,
      isNew: true,
      isFeatured: true,
      rating: 4.7,
      reviewCount: 11
    }
  ];

  // Best selling products (subset of featured products)
  get bestSellers() {
    return this.featuredProducts
      .filter(product => product.reviewCount > 10 && product.rating >= 4.7)
      .sort((a, b) => b.reviewCount - a.reviewCount)
      .slice(0, 4);
  }

  // New arrivals (subset of featured products)
  get newArrivals() {
    return this.featuredProducts
      .filter(product => product.isNew)
      .slice(0, 4);
  }

  // Sale products (subset of featured products)
  get onSale() {
    return this.featuredProducts
      .filter(product => product.salePrice !== null)
      .slice(0, 4);
  }

  // Filter and sort functionality
  activeCategory: string = 'all';
  sortOption: string = 'featured';
  searchQuery: string = '';

  setActiveCategory(category: string) {
    this.activeCategory = category;
  }

  setSortOption(option: string) {
    this.sortOption = option;
  }

  get filteredProducts() {
    let filtered = this.featuredProducts;

    // Apply category filter
    if (this.activeCategory !== 'all') {
      filtered = filtered.filter(product =>
        product.category.toLowerCase() === this.activeCategory.toLowerCase()
      );
    }

    // Apply search filter if there's a query
    if (this.searchQuery.trim() !== '') {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.artist.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    switch (this.sortOption) {
      case 'price-low':
        return filtered.sort((a, b) =>
          (a.salePrice || a.price) - (b.salePrice || b.price)
        );
      case 'price-high':
        return filtered.sort((a, b) =>
          (b.salePrice || b.price) - (a.salePrice || a.price)
        );
      case 'newest':
        return filtered.filter(p => p.isNew).concat(filtered.filter(p => !p.isNew));
      case 'rating':
        return filtered.sort((a, b) => b.rating - a.rating);
      case 'featured':
      default:
        return filtered;
    }
  }

  // Shopping cart functionality (simplified)
  cartItems: number = 0;

  addToCart(productId: number) {
    this.cartItems++;
    // In a real app, this would add the product to a cart service
    console.log(`Added product ${productId} to cart`);
  }
}
