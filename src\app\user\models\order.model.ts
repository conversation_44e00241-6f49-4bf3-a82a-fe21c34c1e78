export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

export interface Order {
  id: number;
  orderNumber: string;
  userId: number;
  orderDate: Date;
  status: string; // 'Processing', 'Shipped', 'Delivered', 'Cancelled'
  items: OrderItem[];
  totalAmount: number;
  shippingAddress: {
    name: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phoneNumber: string;
  };
  paymentMethod: string;
  paymentStatus: string; // 'Pending', 'Paid', 'Failed', 'Refunded'
  shippingMethod: string;
  trackingNumber?: string;
  estimatedDeliveryDate?: Date;
  notes?: string;
}
