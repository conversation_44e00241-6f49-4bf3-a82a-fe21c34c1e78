/* Custom styles for orders component */
:host {
  display: block;
  width: 100%;
}

/* Button hover animation */
button:not(:disabled):hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease-in-out;
}

/* Button active animation */
button:not(:disabled):active {
  transform: translateY(0);
}

/* Card hover effect */
.bg-white.rounded-lg.shadow-md {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.bg-white.rounded-lg.shadow-md:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
