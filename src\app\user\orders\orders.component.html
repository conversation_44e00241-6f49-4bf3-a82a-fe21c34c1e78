<div class="container mx-auto px-4 py-8 max-w-6xl">
  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-heading font-bold text-gray-800 mb-2">My Orders</h1>
    <p class="text-gray-600">Track and manage your orders</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
    <p class="font-medium">Error</p>
    <p>{{ error }}</p>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <!-- Search -->
      <div class="w-full md:w-1/3">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Orders</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="material-icons text-gray-400 text-sm">search</span>
          </div>
          <input
            type="text"
            id="search"
            [(ngModel)]="searchTerm"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Search by order number or product"
          >
        </div>
      </div>

      <!-- Status Filter -->
      <div class="flex flex-wrap gap-2">
        <button
          (click)="applyStatusFilter('')"
          class="px-3 py-1 text-xs font-medium rounded-full transition-colors"
          [ngClass]="statusFilter === '' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
        >
          All
        </button>
        <button
          (click)="applyStatusFilter('Processing')"
          class="px-3 py-1 text-xs font-medium rounded-full transition-colors"
          [ngClass]="statusFilter === 'Processing' ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-700 hover:bg-blue-200'"
        >
          Processing
        </button>
        <button
          (click)="applyStatusFilter('Shipped')"
          class="px-3 py-1 text-xs font-medium rounded-full transition-colors"
          [ngClass]="statusFilter === 'Shipped' ? 'bg-purple-600 text-white' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'"
        >
          Shipped
        </button>
        <button
          (click)="applyStatusFilter('Delivered')"
          class="px-3 py-1 text-xs font-medium rounded-full transition-colors"
          [ngClass]="statusFilter === 'Delivered' ? 'bg-green-600 text-white' : 'bg-green-100 text-green-700 hover:bg-green-200'"
        >
          Delivered
        </button>
        <button
          (click)="applyStatusFilter('Cancelled')"
          class="px-3 py-1 text-xs font-medium rounded-full transition-colors"
          [ngClass]="statusFilter === 'Cancelled' ? 'bg-red-600 text-white' : 'bg-red-100 text-red-700 hover:bg-red-200'"
        >
          Cancelled
        </button>
      </div>

      <!-- Clear Filters -->
      <button
        *ngIf="statusFilter || searchTerm"
        (click)="clearFilters()"
        class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
      >
        <span class="material-icons text-sm mr-1">clear</span>
        Clear Filters
      </button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="flex justify-center my-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
  </div>

  <!-- Orders List -->
  <div *ngIf="!loading">
    <!-- No Orders Message -->
    <div *ngIf="filteredOrders.length === 0" class="bg-white rounded-lg shadow-md p-12 text-center">
      <div class="flex flex-col items-center">
        <span class="material-icons text-gray-400 text-6xl mb-4">shopping_bag</span>
        <h3 class="text-xl font-medium text-gray-700 mb-2">No orders found</h3>
        <p class="text-gray-500 mb-6" *ngIf="statusFilter || searchTerm">Try adjusting your filters</p>
        <p class="text-gray-500 mb-6" *ngIf="!statusFilter && !searchTerm">You haven't placed any orders yet</p>
        <a routerLink="/shop" class="btn btn-primary py-2 px-6 rounded-md">
          Browse Products
        </a>
      </div>
    </div>

    <!-- Orders Cards -->
    <div *ngIf="filteredOrders.length > 0" class="space-y-6">
      <div *ngFor="let order of paginatedOrders" class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Order Header -->
        <div class="p-4 md:p-6 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <div class="flex items-center">
              <h3 class="text-lg font-medium text-gray-900">Order #{{ order.orderNumber }}</h3>
              <span
                class="ml-3 px-2.5 py-0.5 rounded-full text-xs font-medium"
                [ngClass]="getOrderStatusClass(order.status)"
              >
                {{ order.status }}
              </span>
            </div>
            <p class="text-sm text-gray-500 mt-1">Placed on {{ formatDate(order.orderDate) }}</p>
          </div>

          <div class="flex items-center space-x-4">
            <span class="text-lg font-medium text-gray-900">{{ formatCurrency(order.totalAmount) }}</span>
            <a
              [routerLink]="['/orders', order.id]"
              class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
            >
              View Details
              <span class="material-icons text-sm ml-1">arrow_forward</span>
            </a>
          </div>
        </div>

        <!-- Order Items -->
        <div class="p-4 md:p-6">
          <div class="space-y-4">
            <div *ngFor="let item of order.items" class="flex flex-col sm:flex-row sm:items-center gap-4">
              <div class="flex-shrink-0 w-20 h-20">
                <img [src]="item.productImage" [alt]="item.productName" class="w-full h-full object-cover rounded-md">
              </div>
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">{{ item.productName }}</h4>
                <p class="text-sm text-gray-500 mt-1">Qty: {{ item.quantity }}</p>
              </div>
              <div class="text-sm font-medium text-gray-900">
                {{ formatCurrency(item.totalPrice) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Order Footer -->
        <div class="bg-gray-50 p-4 md:p-6 border-t border-gray-200 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div *ngIf="order.trackingNumber" class="flex items-center text-sm text-gray-500">
              <span class="material-icons text-sm mr-1">local_shipping</span>
              Tracking: {{ order.trackingNumber }}
            </div>
            <div *ngIf="order.estimatedDeliveryDate && order.status !== 'Delivered' && order.status !== 'Cancelled'" class="flex items-center text-sm text-gray-500 mt-1">
              <span class="material-icons text-sm mr-1">event</span>
              Estimated delivery: {{ formatDate(order.estimatedDeliveryDate) }}
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <button
              *ngIf="order.status === 'Processing'"
              class="px-4 py-2 border border-red-300 text-red-700 rounded-md text-sm font-medium hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Cancel Order
            </button>
            <a
              *ngIf="order.status === 'Delivered'"
              class="px-4 py-2 border border-primary-300 text-primary-700 rounded-md text-sm font-medium hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Write Review
            </a>
            <button
              class="text-gray-500 hover:text-gray-700"
            >
              <span class="material-icons">more_vert</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalPages > 1" class="flex justify-center mt-8">
      <nav class="flex items-center space-x-2">
        <button
          (click)="changePage(currentPage - 1)"
          [disabled]="currentPage === 1"
          class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span class="material-icons text-sm">chevron_left</span>
        </button>

        <button
          *ngFor="let page of getPageNumbers()"
          (click)="changePage(page)"
          class="px-3 py-1 rounded-md border text-sm font-medium"
          [ngClass]="page === currentPage ? 'bg-primary-600 text-white border-primary-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
        >
          {{ page }}
        </button>

        <button
          (click)="changePage(currentPage + 1)"
          [disabled]="currentPage === totalPages"
          class="px-3 py-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span class="material-icons text-sm">chevron_right</span>
        </button>
      </nav>
    </div>
  </div>
</div>
