/* Custom styles for profile component */
:host {
  display: block;
  width: 100%;
}

/* Form focus animation */
input:focus:not([disabled]),
select:focus:not([disabled]),
textarea:focus:not([disabled]) {
  /* apply transition-all duration-300 ease-in-out; */
  transform: translateY(-1px);
}

/* Button hover animation */
button[type="submit"]:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(193, 68, 14, 0.1), 0 2px 4px -1px rgba(193, 68, 14, 0.06);
}

/* Button active animation */
button[type="submit"]:not(:disabled):active {
  transform: translateY(0);
}
