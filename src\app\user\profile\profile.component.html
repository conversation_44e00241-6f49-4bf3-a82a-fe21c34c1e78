<div class="container mx-auto px-4 py-8 max-w-5xl">
  <!-- <PERSON> Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-heading font-bold text-gray-800 mb-2">My Profile</h1>
    <p class="text-gray-600">Manage your personal information and preferences</p>
  </div>

  <!-- Success/Error Messages -->
  <div *ngIf="success" class="mb-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded" role="alert">
    <p class="font-medium">Success</p>
    <p>{{ success }}</p>
  </div>

  <div *ngIf="error" class="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
    <p class="font-medium">Error</p>
    <p>{{ error }}</p>
  </div>

  <!-- Profile Content -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    <!-- Left Sidebar - User Info Card -->
    <div class="col-span-1">
      <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
        <!-- User Avatar and Name -->
        <div class="p-6 text-center border-b border-gray-200">
          <div class="w-24 h-24 rounded-full bg-primary-100 mx-auto flex items-center justify-center text-primary-600 text-3xl font-medium mb-4">
            {{ user?.firstName?.charAt(0) || user?.username?.charAt(0) || 'U' }}
          </div>
          <h2 class="text-xl font-semibold text-gray-800">{{ user?.firstName }} {{ user?.lastName }}</h2>
          <p class="text-gray-600 mt-1">{{ user?.email }}</p>
          <p class="text-gray-500 text-sm mt-2">Member since Recently</p>
        </div>

        <!-- Quick Links -->
        <div class="p-6">
          <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-4">Account</h3>
          <ul class="space-y-3">
            <li>
              <a routerLink="/profile" class="flex items-center text-primary-600 font-medium">
                <span class="material-icons mr-2 text-sm">person</span>
                Personal Information
              </a>
            </li>
            <li>
              <a routerLink="/orders" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors">
                <span class="material-icons mr-2 text-sm">shopping_bag</span>
                My Orders
              </a>
            </li>
            <li>
              <a routerLink="/wishlist" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors">
                <span class="material-icons mr-2 text-sm">favorite</span>
                Wishlist
              </a>
            </li>
            <li>
              <a routerLink="/addresses" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors">
                <span class="material-icons mr-2 text-sm">location_on</span>
                Addresses
              </a>
            </li>
            <li>
              <a routerLink="/change-password" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors">
                <span class="material-icons mr-2 text-sm">lock</span>
                Change Password
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Right Content - Profile Form -->
    <div class="col-span-1 md:col-span-2">
      <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
        <div class="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-xl font-semibold text-gray-800">Personal Information</h2>
          <button 
            *ngIf="!isEditing" 
            (click)="toggleEdit()" 
            class="text-primary-600 hover:text-primary-700 flex items-center">
            <span class="material-icons mr-1 text-sm">edit</span>
            Edit
          </button>
        </div>

        <div class="p-6">
          <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
            <!-- Name Fields (2 columns) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input 
                  type="text" 
                  id="firstName" 
                  formControlName="firstName"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  [ngClass]="{'bg-gray-100': !isEditing, 'border-red-500': isEditing && f['firstName'].errors && f['firstName'].touched}"
                >
                <div *ngIf="isEditing && f['firstName'].errors && f['firstName'].touched" class="mt-1 text-red-500 text-xs">
                  <div *ngIf="f['firstName'].errors['required']">First name is required</div>
                </div>
              </div>

              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input 
                  type="text" 
                  id="lastName" 
                  formControlName="lastName"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  [ngClass]="{'bg-gray-100': !isEditing, 'border-red-500': isEditing && f['lastName'].errors && f['lastName'].touched}"
                >
                <div *ngIf="isEditing && f['lastName'].errors && f['lastName'].touched" class="mt-1 text-red-500 text-xs">
                  <div *ngIf="f['lastName'].errors['required']">Last name is required</div>
                </div>
              </div>
            </div>

            <!-- Email Field -->
            <div class="mb-6">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <input 
                type="email" 
                id="email" 
                formControlName="email"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-gray-100"
              >
              <p class="mt-1 text-xs text-gray-500">To change your email, please contact customer support</p>
            </div>

            <!-- Phone Number Field -->
            <div class="mb-6">
              <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input 
                type="tel" 
                id="phoneNumber" 
                formControlName="phoneNumber"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                [ngClass]="{'bg-gray-100': !isEditing, 'border-red-500': isEditing && f['phoneNumber'].errors && f['phoneNumber'].touched}"
              >
              <div *ngIf="isEditing && f['phoneNumber'].errors && f['phoneNumber'].touched" class="mt-1 text-red-500 text-xs">
                <div *ngIf="f['phoneNumber'].errors['pattern']">Please enter a valid phone number</div>
              </div>
            </div>

            <!-- Address Fields -->
            <div class="mb-6">
              <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <input 
                type="text" 
                id="address" 
                formControlName="address"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                [ngClass]="{'bg-gray-100': !isEditing}"
              >
            </div>

            <!-- City, State, Zip (3 columns) -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                <input 
                  type="text" 
                  id="city" 
                  formControlName="city"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  [ngClass]="{'bg-gray-100': !isEditing}"
                >
              </div>

              <div>
                <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                <input 
                  type="text" 
                  id="state" 
                  formControlName="state"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  [ngClass]="{'bg-gray-100': !isEditing}"
                >
              </div>

              <div>
                <label for="zipCode" class="block text-sm font-medium text-gray-700 mb-1">ZIP / Postal Code</label>
                <input 
                  type="text" 
                  id="zipCode" 
                  formControlName="zipCode"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  [ngClass]="{'bg-gray-100': !isEditing}"
                >
              </div>
            </div>

            <!-- Country Field -->
            <div class="mb-6">
              <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
              <input 
                type="text" 
                id="country" 
                formControlName="country"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                [ngClass]="{'bg-gray-100': !isEditing}"
              >
            </div>

            <!-- Form Buttons -->
            <div *ngIf="isEditing" class="flex justify-end space-x-4 mt-8">
              <button 
                type="button" 
                (click)="cancelEdit()" 
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button 
                type="submit" 
                [disabled]="loading || profileForm.invalid"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center"
              >
                <span *ngIf="loading" class="mr-2">
                  <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
