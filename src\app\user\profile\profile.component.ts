import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AuthService, User } from '../../auth/services/auth.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  profileForm!: FormGroup;
  user: User | null = null;
  loading = false;
  success = '';
  error = '';
  isEditing = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.user = this.authService.currentUserValue;

    this.profileForm = this.formBuilder.group({
      firstName: [this.user?.firstName || '', Validators.required],
      lastName: [this.user?.lastName || '', Validators.required],
      email: [this.user?.email || '', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.pattern(/^\+?[0-9\s-]{10,15}$/)]],
      address: [''],
      city: [''],
      state: [''],
      zipCode: [''],
      country: ['']
    });

    // Disable form initially
    this.profileForm.disable();
  }

  // Convenience getter for easy access to form fields
  get f() { return this.profileForm.controls; }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;

    if (this.isEditing) {
      this.profileForm.enable();
      // Keep email disabled as it's typically not changed easily
      this.f['email'].disable();
    } else {
      this.profileForm.disable();
    }
  }

  onSubmit(): void {
    if (this.profileForm.invalid) {
      return;
    }

    this.loading = true;
    this.success = '';
    this.error = '';

    // In a real app, this would call the API to update the user profile
    // For demo, we'll just simulate a successful update
    setTimeout(() => {
      // Update the user object with form values
      if (this.user) {
        this.user = {
          ...this.user,
          firstName: this.f['firstName'].value,
          lastName: this.f['lastName'].value
          // Other fields would be updated here
        };

        // Update the auth service
        // In a real app, this would be done by the API response
        this.authService.updateUserProfile(this.user);

        this.success = 'Profile updated successfully';
        this.loading = false;
        this.isEditing = false;
        this.profileForm.disable();
      }
    }, 1000);
  }

  cancelEdit(): void {
    // Reset form to original values
    this.profileForm.patchValue({
      firstName: this.user?.firstName || '',
      lastName: this.user?.lastName || '',
      email: this.user?.email || '',
      // Reset other fields as needed
    });

    this.isEditing = false;
    this.profileForm.disable();
  }
}
