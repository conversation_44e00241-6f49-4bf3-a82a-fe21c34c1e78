import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Order } from '../models/order.model';
import { AuthService } from '../../auth/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class OrdersService {
  private apiUrl = 'api/orders';
  
  // Mock data for demo purposes
  private mockOrders: Order[] = [
    {
      id: 1,
      orderNumber: 'MG-2023-001',
      userId: 2,
      orderDate: new Date('2023-05-15'),
      status: 'Delivered',
      items: [
        {
          id: 1,
          productId: 101,
          productName: 'Madhubani Painting - Krishna with Peacock',
          productImage: 'https://i.etsystatic.com/********/r/il/b7e1b3/4011582218/il_794xN.4011582218_7zxq.jpg',
          quantity: 1,
          price: 12500,
          totalPrice: 12500
        }
      ],
      totalAmount: 12500,
      shippingAddress: {
        name: 'Demo User',
        addressLine1: '123 Main Street',
        city: 'Patna',
        state: 'Bihar',
        postalCode: '800001',
        country: 'India',
        phoneNumber: '+91 **********'
      },
      paymentMethod: 'Credit Card',
      paymentStatus: 'Paid',
      shippingMethod: 'Standard Shipping',
      trackingNumber: 'IND123456789',
      estimatedDeliveryDate: new Date('2023-05-25')
    },
    {
      id: 2,
      orderNumber: 'MG-2023-002',
      userId: 2,
      orderDate: new Date('2023-06-20'),
      status: 'Shipped',
      items: [
        {
          id: 2,
          productId: 102,
          productName: 'Mithila Art Wall Hanging - Tree of Life',
          productImage: 'https://i.etsystatic.com/********/r/il/a7c92e/**********/il_794xN.**********_qxv8.jpg',
          quantity: 1,
          price: 8500,
          totalPrice: 8500
        },
        {
          id: 3,
          productId: 103,
          productName: 'Handcrafted Mithila Earrings',
          productImage: 'https://i.etsystatic.com/16022759/r/il/b2a3a0/4775121316/il_794xN.4775121316_rrw0.jpg',
          quantity: 2,
          price: 1200,
          totalPrice: 2400
        }
      ],
      totalAmount: 10900,
      shippingAddress: {
        name: 'Demo User',
        addressLine1: '123 Main Street',
        city: 'Patna',
        state: 'Bihar',
        postalCode: '800001',
        country: 'India',
        phoneNumber: '+91 **********'
      },
      paymentMethod: 'UPI',
      paymentStatus: 'Paid',
      shippingMethod: 'Express Shipping',
      trackingNumber: 'IND987654321',
      estimatedDeliveryDate: new Date('2023-06-27')
    },
    {
      id: 3,
      orderNumber: 'MG-2023-003',
      userId: 2,
      orderDate: new Date('2023-07-10'),
      status: 'Processing',
      items: [
        {
          id: 4,
          productId: 104,
          productName: 'Mithila Art Cushion Cover Set',
          productImage: 'https://i.etsystatic.com/********/r/il/7f0d5b/**********/il_794xN.**********_k3up.jpg',
          quantity: 3,
          price: 1500,
          totalPrice: 4500
        }
      ],
      totalAmount: 4500,
      shippingAddress: {
        name: 'Demo User',
        addressLine1: '123 Main Street',
        city: 'Patna',
        state: 'Bihar',
        postalCode: '800001',
        country: 'India',
        phoneNumber: '+91 **********'
      },
      paymentMethod: 'Net Banking',
      paymentStatus: 'Paid',
      shippingMethod: 'Standard Shipping',
      estimatedDeliveryDate: new Date('2023-07-20')
    },
    {
      id: 4,
      orderNumber: 'MG-2023-004',
      userId: 2,
      orderDate: new Date('2023-04-05'),
      status: 'Cancelled',
      items: [
        {
          id: 5,
          productId: 105,
          productName: 'Madhubani Painting - Radha Krishna',
          productImage: 'https://i.etsystatic.com/********/r/il/a7c92e/**********/il_794xN.**********_qxv8.jpg',
          quantity: 1,
          price: 15000,
          totalPrice: 15000
        }
      ],
      totalAmount: 15000,
      shippingAddress: {
        name: 'Demo User',
        addressLine1: '123 Main Street',
        city: 'Patna',
        state: 'Bihar',
        postalCode: '800001',
        country: 'India',
        phoneNumber: '+91 **********'
      },
      paymentMethod: 'Credit Card',
      paymentStatus: 'Refunded',
      shippingMethod: 'Express Shipping',
      notes: 'Cancelled by customer due to change of mind.'
    },
    {
      id: 5,
      orderNumber: 'MG-2023-005',
      userId: 2,
      orderDate: new Date('2023-08-01'),
      status: 'Processing',
      items: [
        {
          id: 6,
          productId: 106,
          productName: 'Mithila Art Table Runner',
          productImage: 'https://i.etsystatic.com/********/r/il/7f0d5b/**********/il_794xN.**********_k3up.jpg',
          quantity: 1,
          price: 3500,
          totalPrice: 3500
        },
        {
          id: 7,
          productId: 107,
          productName: 'Handmade Mithila Art Greeting Cards (Set of 5)',
          productImage: 'https://i.etsystatic.com/16022759/r/il/b2a3a0/4775121316/il_794xN.4775121316_rrw0.jpg',
          quantity: 2,
          price: 750,
          totalPrice: 1500
        }
      ],
      totalAmount: 5000,
      shippingAddress: {
        name: 'Demo User',
        addressLine1: '123 Main Street',
        city: 'Patna',
        state: 'Bihar',
        postalCode: '800001',
        country: 'India',
        phoneNumber: '+91 **********'
      },
      paymentMethod: 'UPI',
      paymentStatus: 'Paid',
      shippingMethod: 'Standard Shipping',
      estimatedDeliveryDate: new Date('2023-08-10')
    }
  ];

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}
  
  getUserOrders(): Observable<Order[]> {
    // In a real app, this would call the API with the user's ID
    // const userId = this.authService.currentUserValue?.id;
    // return this.http.get<Order[]>(`${this.apiUrl}/user/${userId}`);
    
    // For demo, return mock data with simulated delay
    return of(this.mockOrders).pipe(delay(800));
  }
  
  getOrderById(orderId: number): Observable<Order> {
    // In a real app, this would call the API
    // return this.http.get<Order>(`${this.apiUrl}/${orderId}`);
    
    // For demo, find in mock data with simulated delay
    const order = this.mockOrders.find(o => o.id === orderId);
    return of(order as Order).pipe(delay(500));
  }
  
  cancelOrder(orderId: number): Observable<any> {
    // In a real app, this would call the API
    // return this.http.post<any>(`${this.apiUrl}/${orderId}/cancel`, {});
    
    // For demo, update mock data with simulated delay
    const orderIndex = this.mockOrders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
      this.mockOrders[orderIndex].status = 'Cancelled';
      this.mockOrders[orderIndex].paymentStatus = 'Refunded';
      this.mockOrders[orderIndex].notes = 'Cancelled by customer.';
    }
    
    return of({ success: true, message: 'Order cancelled successfully' }).pipe(delay(800));
  }
}
