/* You can add global styles to this file, and also import other style files */
/* Custom Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@400;700&family=Playfair+Display:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Caveat:wght@400;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  body {
    @apply font-body text-background-dark bg-background-light overflow-x-hidden pt-20;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold;
  }
  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }
  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }
  h3 {
    @apply text-xl sm:text-2xl md:text-3xl;
  }
  h4 {
    @apply text-lg sm:text-xl md:text-2xl;
  }
  h5 {
    @apply text-base sm:text-lg md:text-xl;
  }
  h6 {
    @apply text-sm sm:text-base md:text-lg;
  }
  a {
    @apply text-primary-500 hover:text-primary-600 transition-colors duration-300;
  }

  /* Responsive container padding */
  .container {
    @apply px-4 sm:px-6 lg:px-8;
  }
}

@layer components {
  .btn {
    @apply px-5 py-3 rounded-md font-medium transition-all duration-300 transform hover:-translate-y-1 hover:shadow-md;
  }
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }
  .btn-secondary {
    @apply bg-secondary-500 text-background-dark hover:bg-secondary-600;
  }
  .btn-accent {
    @apply bg-accent-500 text-white hover:bg-accent-600;
  }
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600;
  }
  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-50;
  }
  .btn-outline-secondary {
    @apply border-2 border-secondary-500 text-secondary-600 hover:bg-secondary-50;
  }
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
  }
  .input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  .section {
    @apply py-16 md:py-20 lg:py-24;
  }
  .section-alt {
    @apply py-16 md:py-20 lg:py-24 bg-primary-50;
  }
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  .heading-decorated {
    @apply relative inline-block;
  }
  .heading-decorated::after {
    @apply content-[''] absolute -bottom-2 left-0 w-1/3 h-1 bg-primary-500;
  }
  .mithila-card {
    @apply relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }
}

/* Mithila Art Specific Styles */
.mithila-border {
  @apply border-4 border-mithila-red p-1 relative;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(244, 180, 0, 0.15) 10px, rgba(244, 180, 0, 0.15) 20px);
}

.mithila-border::before {
  @apply content-[''] absolute -top-2 -left-2 w-4 h-4 border-t-4 border-l-4 border-mithila-blue;
}

.mithila-border::after {
  @apply content-[''] absolute -bottom-2 -right-2 w-4 h-4 border-b-4 border-r-4 border-mithila-blue;
}

.mithila-card {
  @apply card p-6 border-t-4 border-mithila-red;
}

.mithila-heading {
  @apply font-display text-3xl md:text-4xl lg:text-5xl text-mithila-red leading-tight;
}

.mithila-pattern-bg {
  background-color: #FAF8F1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23C1440E' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.mithila-divider {
  @apply relative h-4 my-8;
}

.mithila-divider::before {
  @apply content-[''] absolute top-1/2 left-0 w-full h-0.5 bg-mithila-red bg-opacity-20;
}

.mithila-divider::after {
  @apply content-[''] absolute top-1/2 left-1/2 w-16 h-0.5 bg-mithila-red -translate-x-1/2 -translate-y-1/2;
}

/* Admin Module Styles */
.admin-body {
  @apply font-admin bg-gray-100 text-gray-800;
}

.admin-card {
  @apply bg-white rounded-lg shadow-md p-6;
}

.admin-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500;
}

.admin-btn {
  @apply px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.admin-btn-primary {
  @apply bg-purple-600 text-white hover:bg-purple-700 focus:ring-purple-500;
}

.admin-btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-purple-500;
}

.admin-btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.admin-table {
  @apply min-w-full divide-y divide-gray-200;
}

.admin-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-table td {
  @apply px-6 py-4 whitespace-nowrap;
}

.admin-badge {
  @apply px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.admin-badge-success {
  @apply bg-green-100 text-green-800;
}

.admin-badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.admin-badge-danger {
  @apply bg-red-100 text-red-800;
}

.admin-badge-info {
  @apply bg-blue-100 text-blue-800;
}
