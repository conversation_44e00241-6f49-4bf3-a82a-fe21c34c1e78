/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#FCE9E1',
          100: '#F9D3C3',
          200: '#F5A786',
          300: '#F07B49',
          400: '#EB5A1C',
          500: '#C1440E', // Main primary color - Terracotta Red
          600: '#9A360B',
          700: '#732908',
          800: '#4D1B05',
          900: '#260E03',
        },
        secondary: {
          50: '#FEF9E7',
          100: '#FCF3CF',
          200: '#FAE69F',
          300: '#F7DA6F',
          400: '#F5CD3F',
          500: '#F4B400', // Main secondary color - Sunflower Yellow
          600: '#C39000',
          700: '#926C00',
          800: '#624800',
          900: '#312400',
        },
        accent: {
          50: '#E0F5F5',
          100: '#C1EBEB',
          200: '#83D6D6',
          300: '#45C2C2',
          400: '#16ADAD',
          500: '#008C8C', // Main accent color - Peacock Blue
          600: '#007070',
          700: '#005454',
          800: '#003838',
          900: '#001C1C',
        },
        success: {
          50: '#E8F5EE',
          100: '#D1EBDD',
          200: '#A3D7BB',
          300: '#75C399',
          400: '#57B683',
          500: '#3B945E', // Leaf Green
          600: '#2F764B',
          700: '#235938',
          800: '#183B26',
          900: '#0C1E13',
        },
        background: {
          light: '#FAF8F1', // Ivory/Off-white
          dark: '#333333',  // Charcoal Gray
        },
        mithila: {
          red: '#C1440E',     // Terracotta Red
          yellow: '#F4B400',  // Sunflower Yellow
          blue: '#008C8C',    // Peacock Blue
          green: '#3B945E',   // Leaf Green
          ivory: '#FAF8F1',   // Ivory/Off-white
          charcoal: '#333333', // Charcoal Gray
        }
      },
      fontFamily: {
        sans: ['Poppins', 'Open Sans', 'Lato', 'Inter', 'sans-serif'],
        serif: ['Merriweather', 'serif'],
        display: ['Playfair Display', 'Cinzel', 'serif'],
        handwritten: ['Caveat', 'cursive'],
        heading: ['Playfair Display', 'serif'],
        body: ['Poppins', 'Inter', 'sans-serif'],
        admin: ['Poppins', 'sans-serif'],
      },
      backgroundImage: {
        'mithila-pattern': "url('/assets/images/mithila-pattern.png')",
        'hero-image': "url('/assets/images/hero-image.jpg')",
      },
      animation: {
        'float-slow': 'float 8s ease-in-out infinite',
        'float-medium': 'float 6s ease-in-out infinite',
        'float-fast': 'float 4s ease-in-out infinite',
        'pulse-slow': 'pulse 6s ease-in-out infinite',
        'pulse-medium': 'pulse 4s ease-in-out infinite',
        'spin-slow': 'spin 12s linear infinite',
        'reverse-spin-slow': 'reverse-spin 15s linear infinite',
        'ping-slow': 'ping 3s cubic-bezier(0, 0, 0.2, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
        'shimmer': 'shimmer 2s linear infinite',
        'width-expand': 'width-expand 1.5s forwards',
        'text-glow': 'text-glow 2s ease-out forwards',
        'corner-top-left': 'corner-expand 3s ease-out forwards',
        'corner-top-right': 'corner-expand 3s ease-out 0.5s forwards',
        'corner-bottom-left': 'corner-expand 3s ease-out 1s forwards',
        'corner-bottom-right': 'corner-expand 3s ease-out 1.5s forwards',
        'letter-fade-in-1': 'letter-fade-in 0.5s forwards 0.1s',
        'letter-fade-in-2': 'letter-fade-in 0.5s forwards 0.2s',
        'letter-fade-in-3': 'letter-fade-in 0.5s forwards 0.3s',
        'letter-fade-in-4': 'letter-fade-in 0.5s forwards 0.4s',
        'letter-fade-in-5': 'letter-fade-in 0.5s forwards 0.5s',
        'letter-fade-in-6': 'letter-fade-in 0.5s forwards 0.6s',
        'letter-fade-in-7': 'letter-fade-in 0.5s forwards 0.7s',
        'letter-fade-in-8': 'letter-fade-in 0.5s forwards 0.8s',
        'letter-fade-in-9': 'letter-fade-in 0.5s forwards 0.9s',
        'letter-fade-in-10': 'letter-fade-in 0.5s forwards 1.0s',
        'letter-fade-in-11': 'letter-fade-in 0.5s forwards 1.1s',
        'letter-fade-in-12': 'letter-fade-in 0.5s forwards 1.2s',
        'letter-fade-in-13': 'letter-fade-in 0.5s forwards 1.3s',
        'letter-fade-in-14': 'letter-fade-in 0.5s forwards 1.4s',
        'fade-in-delay-1': 'fade-in 1s forwards 0.5s',
        'word-highlight': 'word-highlight 3s infinite',
        'underline-expand': 'width-expand 1s forwards 1.5s',
        'gradient-background': 'gradient-background 8s ease infinite',
        'gradient-shift': 'gradient-shift 5s ease infinite',
        'gradient-rotate': 'gradient-rotate 8s linear infinite',
        'border-gradient': 'border-gradient 4s linear infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'reverse-spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(-360deg)' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'width-expand': {
          '0%': { width: '0' },
          '100%': { width: '100px' },
        },
        'text-glow': {
          '0%': { textShadow: '0 0 0 rgba(255, 255, 255, 0)' },
          '100%': { textShadow: '0 0 10px rgba(255, 255, 255, 0.5)' },
        },
        'corner-expand': {
          '0%': { width: '0', height: '0', opacity: '0' },
          '100%': { width: '24px', height: '24px', opacity: '1' },
        },
        'letter-fade-in': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'word-highlight': {
          '0%, 100%': { color: 'rgba(244, 180, 0, 1)' },
          '50%': { color: 'rgba(255, 255, 255, 1)' },
        },
        'gradient-background': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        'gradient-shift': {
          '0%, 100%': { transform: 'translateX(0) translateY(0)' },
          '25%': { transform: 'translateX(5%) translateY(-5%)' },
          '50%': { transform: 'translateX(10%) translateY(0%)' },
          '75%': { transform: 'translateX(5%) translateY(5%)' },
        },
        'gradient-rotate': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'border-gradient': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
    },
  },
  plugins: [],
}
